// Inventory system for Idle Miner
// Handles inventory management including artifacts and minerals

import { getArtifactById, formatArtifactDisplay } from './artifacts.js';
import { addArtifactToStorage } from './storage.js';

// Inventory state
let playerInventory = {}; // { itemId: count }
let artifactInventory = {}; // { artifactId: count }
let maxInventorySlots = 8;

// Initialize inventory system
export function initializeInventory() {
  playerInventory = {};
  artifactInventory = {};
  updateInventoryDisplay();
}

// Add item to inventory
export function addToInventory(itemId, quantity = 1, isArtifact = false) {
  const targetInventory = isArtifact ? artifactInventory : playerInventory;
  
  if (!targetInventory[itemId]) {
    targetInventory[itemId] = 0;
  }
  
  targetInventory[itemId] += quantity;
  updateInventoryDisplay();
  
  return true;
}

// Remove item from inventory
export function removeFromInventory(itemId, quantity = 1, isArtifact = false) {
  const targetInventory = isArtifact ? artifactInventory : playerInventory;
  
  if (!targetInventory[itemId] || targetInventory[itemId] < quantity) {
    return false;
  }
  
  targetInventory[itemId] -= quantity;
  if (targetInventory[itemId] <= 0) {
    delete targetInventory[itemId];
  }
  
  updateInventoryDisplay();
  return true;
}

// Get item count in inventory
export function getInventoryCount(itemId, isArtifact = false) {
  const targetInventory = isArtifact ? artifactInventory : playerInventory;
  return targetInventory[itemId] || 0;
}

// Check if inventory has space
export function hasInventorySpace() {
  const totalItems = Object.values(playerInventory).reduce((sum, count) => sum + count, 0);
  return totalItems < maxInventorySlots;
}

// Get current inventory usage
export function getInventoryUsage() {
  const totalItems = Object.values(playerInventory).reduce((sum, count) => sum + count, 0);
  return { used: totalItems, max: maxInventorySlots };
}

// Move artifact to storage
export function moveArtifactToStorage(artifactId) {
  if (!removeFromInventory(artifactId, 1, true)) {
    showNotification('Artifact not found in inventory!');
    return false;
  }
  
  if (addArtifactToStorage(artifactId, 1)) {
    const artifact = getArtifactById(artifactId);
    showNotification(`Moved ${artifact.name} to Storage Room!`);
    return true;
  }
  
  // If storage failed, add back to inventory
  addToInventory(artifactId, 1, true);
  showNotification('Failed to move artifact to storage!');
  return false;
}

// Update inventory display
export function updateInventoryDisplay() {
  updateMineralInventoryDisplay();
  updateArtifactInventoryDisplay();
}

// Update mineral inventory display
function updateMineralInventoryDisplay() {
  const inventoryDiv = document.getElementById('inventory');
  if (!inventoryDiv) return;
  
  const usage = getInventoryUsage();
  let html = `<h3>Inventory (${usage.used}/${usage.max})</h3>`;
  
  if (Object.keys(playerInventory).length === 0) {
    html += '<p style="color: #888;">No minerals in inventory</p>';
  } else {
    Object.entries(playerInventory).forEach(([mineral, count]) => {
      if (count > 0) {
        html += `<div>${mineral}: ${count}</div>`;
      }
    });
  }
  
  inventoryDiv.innerHTML = html;
}

// Update artifact inventory display
function updateArtifactInventoryDisplay() {
  const artifactDiv = document.getElementById('artifact-inventory');
  if (!artifactDiv) return;
  
  let html = '<h3>Artifacts</h3>';
  
  if (Object.keys(artifactInventory).length === 0) {
    html += '<p style="color: #888;">No artifacts in inventory</p>';
  } else {
    Object.entries(artifactInventory).forEach(([artifactId, count]) => {
      const artifact = getArtifactById(artifactId);
      if (artifact && count > 0) {
        html += `
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 6px; background: rgba(255,255,255,0.1); border-radius: 4px;">
            <div>
              <strong>${formatArtifactDisplay(artifact, count)}</strong>
              <br>
              <small style="color: #ccc;">${artifact.description}</small>
            </div>
            <button onclick="moveArtifactToStorage('${artifactId}')" 
                    style="background: #2196F3; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; margin-left: 10px;">
              Store
            </button>
          </div>
        `;
      }
    });
  }
  
  artifactDiv.innerHTML = html;
}

// Set max inventory slots
export function setMaxInventorySlots(slots) {
  maxInventorySlots = slots;
  updateInventoryDisplay();
}

// Get max inventory slots
export function getMaxInventorySlots() {
  return maxInventorySlots;
}

// Clear inventory
export function clearInventory() {
  playerInventory = {};
  artifactInventory = {};
  updateInventoryDisplay();
}

// Get all inventory data
export function getInventoryData() {
  return {
    playerInventory: { ...playerInventory },
    artifactInventory: { ...artifactInventory },
    maxInventorySlots
  };
}

// Load inventory data
export function loadInventoryData(data) {
  if (data) {
    if (data.playerInventory) {
      playerInventory = { ...data.playerInventory };
    }
    if (data.artifactInventory) {
      artifactInventory = { ...data.artifactInventory };
    }
    if (data.maxInventorySlots) {
      maxInventorySlots = data.maxInventorySlots;
    }
    updateInventoryDisplay();
  }
}

// Check if inventory is full
export function isInventoryFull() {
  return !hasInventorySpace();
}

// Get total artifact count in inventory
export function getTotalArtifactCount() {
  return Object.values(artifactInventory).reduce((sum, count) => sum + count, 0);
}

// Utility function to show notifications (fallback if not available globally)
function showNotification(message) {
  if (typeof window !== 'undefined' && typeof window.showNotification === 'function') {
    window.showNotification(message);
  } else {
    console.log('Notification:', message);
  }
}

// Make functions available globally for HTML onclick handlers
if (typeof window !== 'undefined') {
  window.moveArtifactToStorage = moveArtifactToStorage;
}
