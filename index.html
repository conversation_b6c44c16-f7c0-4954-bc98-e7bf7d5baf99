<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Idle Miner</title>
<!--
  FIXES APPLIED:
  1. Events Panel Reset on New Tier - clearEventsOnNewTier() function added
  2. Event Timer Skip Button - skipEventForCoins() function with 200 coin cost
  3. Fix Storage Button - toggleStorage() function properly connected
  4. Drill Enhancement Timing Bug - Fixed timer reset issue in buyDrillEnhancement()
  5. General Bug Fixes - Improved mining interval calculation and timer stability
-->
<style>
  html, body {
    margin: 0;
    height: 100%;
    background-color: #666666;
    font-family: monospace, sans-serif;
    color: #eee;
    user-select: none;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
    min-height: 100vh;
    overflow: hidden;
  }
  button {
    margin: 10px 5px;
    padding: 10px 20px;
    font-size: 16px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  button:hover {
    background-color: rgba(0, 0, 0, 0.9);
  }
  button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  h1, h2, p {
    text-shadow: 2px 2px 5px #000;
    margin: 5px 0 15px 0;
  }
  .ui-container {
    position: relative;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 12px;
    padding: 25px;
    width: 1200px;
    height: 800px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: 1fr 280px;
    grid-template-rows: auto auto 1fr auto;
    grid-gap: 20px;
    box-sizing: border-box;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }

  /* Display size variants */
  .ui-container.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);
  }

  .ui-container.large {
    width: 1200px;
    height: 800px;
    grid-template-columns: 1fr 280px;
    grid-gap: 25px;
  }

  .ui-container.medium {
    width: 600px;
    height: 500px;
    padding: 20px;
    grid-gap: 15px;
  }

  .ui-container.small {
    width: 400px;
    height: 350px;
    padding: 15px;
    grid-gap: 10px;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto auto;
  }

  .ui-container.corner {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 300px;
    height: 250px;
    padding: 10px;
    grid-gap: 8px;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto auto;
    z-index: 999;
    font-size: 12px;
  }

  .header-section {
    grid-column: 1 / -1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    padding: 15px 20px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .title-area h1 {
    margin: 0;
    font-size: 28px;
    color: #fff;
    text-shadow: 2px 2px 8px #000;
  }

  .stats-area {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
  }

  #coin-display {
    font-weight: bold;
    font-size: 24px;
    user-select: text;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  }

  .depth-display {
    font-size: 16px;
    color: #ccc;
  }
  .mining-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px;
    background: rgba(255, 255, 255, 0.03);
    padding: 30px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 0;
  }

  .progress-section {
    display: flex;
    align-items: center;
    gap: 30px;
    width: 100%;
    justify-content: center;
  }

  .inventory-section {
    background: rgba(255, 255, 255, 0.03);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    height: calc(100% - 30px);
    min-height: 0;
    margin-bottom: 30px;
    z-index: 1;
    position: relative;
  }

  .inventory-section h2 {
    margin: 0 0 15px 0;
    text-align: center;
    color: #fff;
    font-size: 20px;
    flex-shrink: 0;
  }

  .inventory-list {
    text-align: left;
    overflow: hidden;
    border: 1px solid #555;
    padding: 15px;
    border-radius: 6px;
    background: rgba(20, 20, 20, 0.7);
    flex: 1;
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-height: 0;
  }

  .inventory-list li {
    margin: 0;
    padding: 6px 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    font-size: 14px;
    flex-shrink: 0;
  }

  .inventory-list li:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .sell-all-btn {
    width: 100%;
    background: linear-gradient(to bottom, #d4a574, #b8935a);
    color: #000;
    font-weight: bold;
    padding: 12px 20px;
    font-size: 16px;
    flex-shrink: 0;
  }

  .sell-all-btn:hover {
    background: linear-gradient(to bottom, #e4b584, #c8a36a);
  }

  .bottom-buttons {
    grid-column: 1 / -1;
    display: flex;
    gap: 15px;
    justify-content: center;
    background: rgba(255, 255, 255, 0.03);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  .panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(20,20,20,0.95);
    border-radius: 12px;
    padding: 25px;
    display: none;
    color: #eee;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    z-index: 1000;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    min-width: 400px;
  }

  .panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
  }

  .panel h2 {
    margin-top: 0;
    margin-bottom: 15px;
    text-align: center;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    padding: 10px 15px;
    border-radius: 8px;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .panel h2:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.02);
  }
  .panel button {
    width: 100%;
    margin-top: 10px;
  }

  .shop-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
  }

  .shop-item p {
    margin: 5px 0;
  }

  .shop-item strong {
    color: #fff;
  }

  .prestige-item {
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
  }

  .prestige-item strong {
    color: #ffd700;
  }

  .settings-group {
    margin-bottom: 20px;
  }

  .settings-group h3 {
    margin: 0 0 10px 0;
    color: #fff;
    font-size: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 5px;
  }

  .size-slider-container {
    margin-bottom: 15px;
  }

  .size-slider-container label {
    display: block;
    margin-bottom: 8px;
    color: #ccc;
    font-size: 14px;
  }

  #size-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #333;
    outline: none;
    margin-bottom: 8px;
  }

  #size-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4a6ea9;
    cursor: pointer;
    border: 2px solid #fff;
  }

  #size-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4a6ea9;
    cursor: pointer;
    border: 2px solid #fff;
  }

  .slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #999;
  }

  /* Responsive adjustments for smaller sizes */
  .ui-container.small .header-section,
  .ui-container.corner .header-section {
    flex-direction: column;
    gap: 5px;
    text-align: center;
    padding: 10px;
  }

  .ui-container.small .title-area h1,
  .ui-container.corner .title-area h1 {
    font-size: 18px;
    margin: 0;
  }

  .ui-container.small .stats-area,
  .ui-container.corner .stats-area {
    align-items: center;
  }

  .ui-container.small #coin-display,
  .ui-container.corner #coin-display {
    font-size: 16px;
  }

  .ui-container.small .depth-display,
  .ui-container.corner .depth-display {
    font-size: 12px;
  }

  .ui-container.small .inventory-section h2,
  .ui-container.corner .inventory-section h2 {
    font-size: 14px;
    margin: 0 0 8px 0;
  }

  .ui-container.small button,
  .ui-container.corner button {
    padding: 6px 12px;
    font-size: 12px;
    margin: 3px 2px;
  }

  .ui-container.corner .mining-controls button {
    padding: 8px 16px;
    font-size: 14px;
  }
  /* Mineral colors */
  .mineral-Feldspar { color: #bdb76b; }
  .mineral-Gypsum { color: #dcdcdc; }
  .mineral-Quartz { color: #f0e68c; }
  .mineral-Mica { color: #eee8aa; }
  .mineral-Calcite { color: #ffebcd; }
  .mineral-Fluorite { color: #98fb98; }
  .mineral-Magnetite { color: #778899; }
  .mineral-Hematite { color: #b22222; }
  .mineral-Dolomite { color: #d3d3d3; }
  .mineral-Apatite { color: #7fffd4; }
  .mineral-Barite { color: #a9a9a9; }
  .mineral-Talc { color: #f5f5f5; }
  .mineral-Bauxite { color: #cd853f; }
  .mineral-Sphalerite { color: #d2b48c; }
  .mineral-Chalcopyrite { color: #b8860b; }
  .mineral-Galena { color: #a9a9a9; }
  .mineral-Silver { color: #c0c0c0; }
  .mineral-Topaz { color: #ffd700; }
  .mineral-Gold { color: #ffd700; }
  .mineral-Sapphire { color: #4682b4; }
  .mineral-Emerald { color: #50c878; }
  .mineral-Ruby { color: #e0115f; }
  .mineral-Diamond { color: #b9f2ff; }
  .mineral-Platinum { color: #e5e4e2; }
  .mineral-Darkstone { color: #343434; }
  .mineral-Emberglass { color: #ff4500; }
  .mineral-Froststeel { color: #add8e6; }
  .mineral-Starcrystal { color: #87ceeb; }
  .mineral-Sunshard { color: #ffd700; }
  .mineral-Voidgem { color: #4b0082; }

  /* Mineral icons */
  .mineral-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
    border: 1px solid rgba(255,255,255,0.3);
    box-shadow: 0 0 4px rgba(0,0,0,0.5);
  }

  .inventory-item {
    display: flex;
    align-items: center;
    min-width: 150px;
  }

  .inventory-value {
    color: gold;
    font-weight: bold;
    margin-right: 10px;
  }

  /* Visual effects */
  .coin-gain {
    position: absolute;
    color: gold;
    font-weight: bold;
    animation: floatUp 1s ease-out forwards;
    pointer-events: none;
    z-index: 100;
    text-shadow: 0 0 5px #000;
  }
  @keyframes floatUp {
    0% { transform: translateY(0); opacity: 1; }
    100% { transform: translateY(-50px); opacity: 0; }
  }
  .notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 1000;
    animation: fadeOut 2s ease-out 1s forwards;
    max-width: 80%;
    text-align: center;
  }
  .notification.success {
    background: rgba(46, 125, 50, 0.9);
    border-left: 5px solid #81c784;
  }
  @keyframes fadeOut {
    0% { opacity: 1; }
    100% { opacity: 0; }
  }

  /* Dynamic Mining Display */
  .mining-display-container {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .mining-visual {
    width: 300px;
    height: 200px;
    background: linear-gradient(to bottom, #8B4513 0%, #654321 30%, #4A4A4A 60%, #2F2F2F 100%);
    border: 3px solid #666;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
  }

  .mining-chunks {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(15, 1fr);
    grid-template-rows: repeat(10, 1fr);
    gap: 1px;
  }

  .chunk {
    background: rgba(139, 69, 19, 0.8);
    border: 1px solid rgba(101, 67, 33, 0.5);
    transition: all 0.3s ease;
    position: relative;
  }

  .chunk.mined {
    background: rgba(0, 0, 0, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.8);
  }

  .chunk.mineral {
    background: radial-gradient(circle, #FFD700, #FFA500);
    border-color: #FFD700;
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
    animation: sparkle 2s infinite;
  }

  .chunk.mineral.mined {
    background: rgba(255, 215, 0, 0.2);
    animation: none;
  }

  @keyframes sparkle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  .mining-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
    font-size: 16px;
    color: #ccc;
  }

  .depth-info {
    text-align: center;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 6px;
    border: 1px solid #666;
  }

  .depth-info h3 {
    margin: 0 0 5px 0;
    color: #fff;
    font-size: 18px;
  }

  .progress-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .stat-line {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .mining-particle {
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: gold;
    pointer-events: none;
    z-index: 90;
    animation: particleFly 1s ease-out forwards;
  }
  @keyframes particleFly {
    0% {
      transform: translate(0, 0);
      opacity: 1;
    }
    100% {
      transform: translate(
        calc(var(--tx) * 50px),
        calc(var(--ty) * 50px)
      );
      opacity: 0;
    }
  }

  .mining-timer {
    text-align: center;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 6px;
    border: 1px solid #666;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    margin-top: 10px;
  }

  .timer-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
  }

  #timer-countdown {
    color: #4CAF50;
    font-family: 'Courier New', monospace;
  }

  .mining-timer.halted #timer-countdown {
    color: #ff6b6b;
  }

  .mining-timer.slowed #timer-countdown {
    color: #ffa500;
  }

  .mining-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .mining-controls button {
    padding: 20px 60px;
    font-size: 24px;
    background: linear-gradient(to bottom, #4a6ea9, #3a5a8a);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    transition: all 0.2s ease;
    border-radius: 8px;
    min-width: 250px;
  }

  .mining-controls button:hover {
    background: linear-gradient(to bottom, #5a7eb9, #4a6a9a);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.3);
  }

  /* Mobile responsiveness */
  @media (max-width: 900px) {
    .ui-container {
      width: 95%;
      padding: 15px;
      height: 90vh;
      grid-template-columns: 1fr;
      grid-template-rows: auto auto auto 1fr auto;
    }

    .header-section {
      flex-direction: column;
      gap: 10px;
      text-align: center;
    }

    .stats-area {
      align-items: center;
    }

    .mining-section {
      order: 1;
    }

    .inventory-section {
      order: 2;
    }

    button {
      padding: 8px 16px;
      font-size: 14px;
    }

    .title-area h1 {
      font-size: 24px;
    }

    .panel {
      padding: 15px;
    }

    .progress-section {
      flex-direction: column;
    }

    .progress-bar {
      width: 25px;
      height: 150px;
    }
  }
</style>
</head>
<body>
  <div class="ui-container" role="main" aria-label="Idle Miner Game UI">
    <!-- Header Section -->
    <div class="header-section">
      <div class="title-area">
        <h1>Idle Miner</h1>
      </div>
      <div class="stats-area">
        <div id="coin-display" aria-live="polite">💰 <span id="coin-count">0</span> Coins</div>
      </div>
    </div>

    <!-- Mining Section -->
    <div class="mining-section">
      <div class="progress-section">
        <div class="mining-display-container">
          <div class="mining-visual">
            <div class="mining-chunks" id="mining-chunks">
              <!-- Chunks will be generated by JavaScript -->
            </div>
          </div>
          <div class="mining-info">
            <div class="depth-info">
              <h3>Current Depth</h3>
              <div id="current-depth">0m</div>
            </div>
            <div class="mining-timer" id="mining-timer">
              <div class="timer-display">
                <span id="timer-text">Next mine in: </span>
                <span id="timer-countdown">--</span>
              </div>
            </div>
            <div class="progress-stats">
              <div class="stat-line">
                <span>Progress:</span>
                <span id="depth-progress-text">0%</span>
              </div>
              <div class="stat-line">
                <span>Next Tier:</span>
                <span id="next-tier">100m</span>
              </div>
              <div class="stat-line">
                <span>Chunks Mined:</span>
                <span id="chunks-mined">0/150</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mining-controls">
        <button onclick="toggleAutoMining()" id="auto-btn" aria-pressed="false">⛏️ Start Mining</button>
      </div>
    </div>

    <!-- Inventory Section -->
    <div class="inventory-section" aria-label="Inventory section">
      <h2>💎 Inventory <span id="inventory-count" style="font-size: 14px; color: #ccc;">(0/8)</span></h2>
      <ul id="inventory" class="inventory-list"></ul>
      <button onclick="sellMinerals()" class="sell-all-btn">💰 Sell All Minerals</button>
    </div>

    <!-- Panel overlay -->
    <div id="panel-overlay" class="panel-overlay" onclick="closeAllPanels()"></div>

    <!-- Shop and Settings panels -->
    <div id="shop-panel" class="panel" aria-hidden="true" aria-label="Shop Section">
      <h2 onclick="closeAllPanels()">🛒 Shop</h2>
      <div class="shop-item">
        <p>⛏️ <strong>Drill Enhancement</strong> (Level <span id="drill-level">0</span>) - 15% faster mining</p>
        <p>Cost: <span id="drill-cost">25</span> coins</p>
        <button onclick="buyDrillEnhancement()">Buy Drill Enhancement</button>
      </div>
      <div class="shop-item">
        <p>💎 <strong>Vein Chance Boost</strong> (+8% Vein Chance)</p>
        <p>Cost: <span id="vein-boost-cost">60</span> coins</p>
        <button onclick="buyVeinChanceBoost()" id="veinChanceBoostBtn">Buy Vein Chance Boost</button>
      </div>
      <div class="shop-item">
        <p>🎒 <strong>Inventory Expansion</strong> (Level <span id="inventory-level">0</span>) - +5 inventory slots</p>
        <p>Cost: <span id="inventory-cost">100</span> coins</p>
        <button onclick="buyInventoryUpgrade()">Buy Inventory Expansion</button>
      </div>
      <div class="shop-item" id="autosell-shop-item" style="display: none;">
        <p>🤖 <strong>Auto-Sell</strong> (Sells minerals at 50% value)</p>
        <p>Cost: <span id="autosell-cost">2000</span> coins</p>
        <button onclick="buyAutoSell()" id="autosell-btn">Buy Auto-Sell</button>
      </div>
      <div class="shop-item prestige-item">
        <p>🚀 <strong>Prestige Reset</strong> (Gain permanent 10% bonus)</p>
        <p>Cost: 5,000 coins</p>
        <button onclick="prestigeReset()" id="prestige-btn">Prestige</button>
      </div>
    </div>

    <div id="settings-panel" class="panel" aria-hidden="true" aria-label="Settings Section">
      <h2 onclick="closeAllPanels()">⚙️ Settings</h2>
      <div class="settings-group">
        <h3>🖥️ Display Settings</h3>
        <div class="size-slider-container">
          <label for="size-slider">Screen Size: <span id="size-display">1200x800</span></label>
          <input type="range" id="size-slider" min="50" max="150" value="100" step="5" onchange="updateScreenSize(this.value)">
          <div class="slider-labels">
            <span>Small</span>
            <span>Medium</span>
            <span>Large</span>
          </div>
        </div>
        <button onclick="toggleFullscreen()" id="fullscreen-btn">🖥️ Toggle Fullscreen</button>
      </div>
      <div class="settings-group">
        <h3>💾 Game Data</h3>
        <button onclick="saveGame()">💾 Save Game</button>
        <button onclick="loadGame()">📁 Load Game</button>
        <button onclick="resetGame()">🔄 Reset Game</button>
      </div>
      <div class="settings-group">
        <h3>🔊 Audio</h3>
        <div style="margin-bottom: 10px;">
          <label for="music-file" style="display: block; margin-bottom: 5px; color: #ccc;">Load Music File (MP3):</label>
          <input type="file" id="music-file" accept="audio/mp3,audio/mpeg" onchange="loadMusicFile(this)" style="width: 100%; margin-bottom: 10px;">
        </div>
        <button onclick="toggleMusic()" id="music-btn">🔈 Music Off</button>
        <button onclick="toggleSoundEffects()" id="sfx-btn">🔉 SFX On</button>
      </div>
    </div>

    <div id="stats-panel" class="panel" aria-hidden="true" aria-label="Game Statistics">
      <h2 onclick="closeAllPanels()">📊 Game Statistics</h2>
      <div id="stats-content"></div>
    </div>

    <div id="encyclopedia-panel" class="panel" aria-hidden="true" aria-label="Mineral Encyclopedia">
      <h2 onclick="closeAllPanels()">📚 Mineral Encyclopedia</h2>

      <div style="margin-bottom: 20px; padding: 15px; background: rgba(255, 255, 255, 0.05); border-radius: 8px; border: 1px solid rgba(255, 255, 255, 0.1);">
        <h3 style="margin-top: 0; color: #fff;">🎮 Visual Mining Simulation</h3>
        <p style="margin: 10px 0; line-height: 1.5;">The mining display shows a 15x10 grid of earth chunks (150 total). Mining progresses <strong>left-to-right, top-to-bottom</strong> in order:</p>
        <ul style="margin: 10px 0; padding-left: 20px; line-height: 1.5;">
          <li><strong>Colored chunks</strong> represent unmined earth (color changes by tier)</li>
          <li><strong>Golden chunks</strong> contain valuable minerals (10% spawn rate)</li>
          <li><strong>Black chunks</strong> have been mined and cleared</li>
          <li><strong>Particle effects</strong> show mining activity</li>
        </ul>
        <p style="margin: 10px 0; line-height: 1.5;"><strong>Tier Colors:</strong> Brown → Gray → Blue → Purple → Red → Orange → Green → Navy → Magenta → Black</p>
        <p style="margin: 10px 0; line-height: 1.5;">When all 150 chunks are mined, the display resets for the next mining tier with new colors and mineral deposits!</p>
      </div>

      <div id="mineral-grid"></div>
    </div>

    <div id="found-minerals-panel" class="panel" aria-hidden="true" aria-label="Found Minerals">
      <h2 onclick="closeAllPanels()">💎 Found Minerals</h2>
      <div id="found-minerals-content">
        <p>Track all the minerals you've discovered during your mining adventures!</p>
        <div id="found-minerals-list"></div>
      </div>
    </div>

    <div id="events-panel" class="panel" aria-hidden="true" aria-label="Current Events">
      <h2 onclick="closeAllPanels()">⚡ Current Events</h2>
      <div id="events-content">
        <p>Random events that affect your mining operations:</p>
        <div id="active-events-list"></div>
        <div id="event-history" style="margin-top: 20px;">
          <h3>Recent Events:</h3>
          <div id="recent-events-list"></div>
        </div>
      </div>
    </div>

    <!-- Storage Room Feature Hook -->
    <div id="storage-panel" class="panel" aria-hidden="true" aria-label="Storage Room">
      <h2 onclick="closeAllPanels()">🏺 Storage Room</h2>
      <div id="storage-content">
        <p>Store your valuable artifacts safely here. Artifacts don't take up inventory space when stored.</p>
        <button onclick="sellAllArtifactsFromStorage()" style="margin-bottom: 15px; background: #ff6b6b; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">💰 Sell All Artifacts</button>
        <div id="storage-list"></div>
      </div>
    </div>

    <div class="bottom-buttons" role="navigation" aria-label="Game options">
      <button id="shop-btn" onclick="toggleShop()" aria-pressed="false" aria-controls="shop-panel">Shop 🛒</button>
      <button id="found-minerals-btn" onclick="toggleFoundMinerals()" aria-pressed="false" aria-controls="found-minerals-panel">Found Minerals 💎</button>
      <!-- Storage Room Button Added -->
      <button id="storage-btn" onclick="toggleStorage()" aria-pressed="false" aria-controls="storage-panel">Storage 🏺</button>
      <button id="stats-btn" onclick="toggleStats()" aria-pressed="false" aria-controls="stats-panel">Stats 📊</button>
      <button id="events-btn" onclick="toggleEvents()" aria-pressed="false" aria-controls="events-panel">Events ⚡</button>
      <button id="encyclopedia-btn" onclick="toggleEncyclopedia()" aria-pressed="false" aria-controls="encyclopedia-panel">Encyclopedia 📚</button>
      <button id="settings-btn" onclick="toggleSettings()" aria-pressed="false" aria-controls="settings-panel">Settings ⚙️</button>
    </div>
  </div>

  <!-- Audio elements -->
  <audio id="bg-music" loop preload="auto">
    <!-- MP3 file will be loaded dynamically via JavaScript -->
    Your browser does not support the audio element.
  </audio>

  <audio id="mining-sound">
    <source src="https://cdn.pixabay.com/download/audio/2021/08/04/audio_bb630cc098.mp3?filename=mining-pickaxe-hit-180745.mp3" type="audio/mpeg" />
  </audio>

  <audio id="sell-sound">
    <source src="https://cdn.pixabay.com/download/audio/2022/03/15/audio_5a5dc266e5.mp3?filename=coins-handling-193701.mp3" type="audio/mpeg" />
  </audio>

  <audio id="upgrade-sound">
    <source src="https://cdn.pixabay.com/download/audio/2021/08/09/audio_3f6a4a0c3b.mp3?filename=magical-harp-6213.mp3" type="audio/mpeg" />
  </audio>

  <script>
    // Game State
    let coins = 0;
    let miningSpeed = 1;
    let currentDepth = 0;
    let depthProgress = 0;
    let currentTier = 0;
    let autoMining = false;
    let autoInterval = null;
    let inventory = {};
    let veinChance = 0;
    let veinChanceBoostCooldown = false;
    let prestigeBonus = 1;
    let totalEarnedCoins = 0;
    let autoSellEnabled = false;
    let mineralsDiscovered = new Set();
    let lastSaveTime = Date.now();
    let soundEffectsEnabled = true;
    let musicEnabled = false;
    let currentScreenScale = 100;
    let isFullscreen = false;

    // Mining display variables
    let miningChunks = [];
    let chunksMinedCount = 0;
    let totalChunks = 150; // 15x10 grid
    let currentMiningIndex = 0; // Track current mining position for left-to-right

    // New upgrade levels
    let drillLevel = 0;
    let inventoryUpgradeLevel = 0;

    // Inventory system
    let maxInventorySlots = 8; // Base inventory limit
    const BASE_INVENTORY_SLOTS = 8;
    const INVENTORY_UPGRADE_SLOTS = 5; // +5 slots per upgrade
    const BASE_INVENTORY_UPGRADE_COST = 100;

    // Found minerals tracking
    let foundMinerals = {}; // Track count of each mineral type found

    // Event system
    let activeEvents = [];
    let eventHistory = [];
    let lastEventTime = 0;
    let eventCooldown = 60000; // 1 minute between events
    let currentEventPopup = null; // Track current event popup

    // Timer system
    let miningTimer = null;
    let nextMineTime = 0;
    let timerUpdateInterval = null;

    // Notification throttling
    let lastInventoryWarning = 0;
    let lastInventoryFullWarning = 0;

    // Tier colors for chunks (excluding gold mineral chunks)
    const tierColors = [
      'rgba(139, 69, 19, 0.8)',    // Tier 0: Brown
      'rgba(105, 105, 105, 0.8)',  // Tier 1: Gray
      'rgba(70, 130, 180, 0.8)',   // Tier 2: Steel Blue
      'rgba(128, 0, 128, 0.8)',    // Tier 3: Purple
      'rgba(220, 20, 60, 0.8)',    // Tier 4: Crimson
      'rgba(255, 140, 0, 0.8)',    // Tier 5: Dark Orange
      'rgba(0, 100, 0, 0.8)',      // Tier 6: Dark Green
      'rgba(25, 25, 112, 0.8)',    // Tier 7: Midnight Blue
      'rgba(139, 0, 139, 0.8)',    // Tier 8: Dark Magenta
      'rgba(0, 0, 0, 0.9)'         // Tier 9: Black
    ];

    // Achievements
    let achievements = {
      firstMineral: false,
      firstVein: false,
      firstUpgrade: false,
      firstSale: false,
      firstPrestige: false
    };

    // Game Statistics
    let gameStats = {
      totalMineralsMined: 0,
      totalVeinsFound: 0,
      timePlayed: 0,
      highestValueMineral: "",
      deepestDepth: 0,
      veinSuccessRate: 0,
      largestVeinFound: 0,
      totalVeinMinerals: 0,
      averageVeinSize: 0
    };

    // Game Balance Constants
    const BASE_MINING_TIME = 30000; // 30 seconds base mining time
    const TIER_TIME_INCREASE = 15000; // +15 seconds per tier
    const BASE_UPGRADE_COST = 25;
    const BASE_VEIN_BOOST_COST = 60;
    const MINING_SPEED_INCREASE = 0.15; // Smaller increments
    const PRESTIGE_REQUIREMENT = 5000;
    const AUTO_SELL_COST = 2000;
    const DEPTH_PER_MINE = 1;
    const DEPTH_TIERS = [100, 300, 600, 1000, 1500, 2100, 2800, 3600, 4500, 5500];

    // New upgrade costs and effects
    const BASE_DRILL_COST = 25;
    const BASE_AUTO_SELL_MINERALS_COST = 500;
    const DRILL_SPEED_BOOST = 0.15; // 15% speed increase per level

    // Event definitions
    const EVENT_TYPES = [
      {
        id: 'broken_drill',
        name: 'Broken Drill Bit',
        description: 'Your drill bit has broken! Mining is halted while repairs are made.',
        icon: '🔧',
        duration: 45000, // 45 seconds
        effect: 'mining_halt'
      },
      {
        id: 'cave_in',
        name: 'Mine Cave-In',
        description: 'A section of the mine has collapsed! Clearing debris takes time.',
        icon: '⛰️',
        duration: 60000, // 60 seconds
        effect: 'mining_halt'
      },
      {
        id: 'equipment_malfunction',
        name: 'Equipment Malfunction',
        description: 'Mining equipment is malfunctioning, reducing efficiency.',
        icon: '⚙️',
        duration: 90000, // 90 seconds
        effect: 'slow_mining',
        multiplier: 0.5 // 50% slower
      },
      {
        id: 'power_outage',
        name: 'Power Outage',
        description: 'Electrical systems are down, severely impacting operations.',
        icon: '⚡',
        duration: 75000, // 75 seconds
        effect: 'slow_mining',
        multiplier: 0.3 // 70% slower
      },
      {
        id: 'gas_leak',
        name: 'Gas Leak',
        description: 'Dangerous gases detected! Mining suspended for safety.',
        icon: '☠️',
        duration: 50000, // 50 seconds
        effect: 'mining_halt'
      }
    ];

    // Mineral tiers and colors
    const mineralData = {
      "Feldspar": { value: 2, color: "#bdb76b" },
      "Gypsum": { value: 2, color: "#dcdcdc" },
      "Quartz": { value: 3, color: "#f0e68c" },
      "Mica": { value: 4, color: "#eee8aa" },
      "Calcite": { value: 5, color: "#ffebcd" },
      "Fluorite": { value: 6, color: "#98fb98" },
      "Magnetite": { value: 7, color: "#778899" },
      "Hematite": { value: 8, color: "#b22222" },
      "Dolomite": { value: 10, color: "#d3d3d3" },
      "Apatite": { value: 12, color: "#7fffd4" },
      "Barite": { value: 13, color: "#a9a9a9" },
      "Talc": { value: 14, color: "#f5f5f5" },
      "Bauxite": { value: 15, color: "#cd853f" },
      "Sphalerite": { value: 18, color: "#d2b48c" },
      "Chalcopyrite": { value: 20, color: "#b8860b" },
      "Galena": { value: 25, color: "#a9a9a9" },
      "Silver": { value: 40, color: "#c0c0c0" },
      "Topaz": { value: 45, color: "#ffd700" },
      "Gold": { value: 50, color: "#ffd700" },
      "Sapphire": { value: 60, color: "#4682b4" },
      "Emerald": { value: 70, color: "#50c878" },
      "Ruby": { value: 80, color: "#e0115f" },
      "Diamond": { value: 100, color: "#b9f2ff" },
      "Platinum": { value: 120, color: "#e5e4e2" },
      "Darkstone": { value: 150, color: "#343434" },
      "Emberglass": { value: 180, color: "#ff4500" },
      "Froststeel": { value: 220, color: "#add8e6" },
      "Starcrystal": { value: 270, color: "#87ceeb" },
      "Sunshard": { value: 320, color: "#ffd700" },
      "Voidgem": { value: 400, color: "#4b0082" }
    };

    // Mineral tiers based on depth
    const mineralTiers = [
      [ // Tier 0 - Surface (0-100m)
        "Feldspar", "Gypsum", "Quartz", "Mica",
        "Calcite", "Fluorite", "Magnetite", "Hematite"
      ],
      [ // Tier 1 - Shallow (100-300m)
        "Dolomite", "Apatite", "Barite", "Talc",
        "Bauxite", "Sphalerite", "Chalcopyrite", "Galena"
      ],
      [ // Tier 2 - Medium (300-600m)
        "Silver", "Topaz", "Gold", "Sapphire",
        "Emerald", "Ruby", "Diamond", "Platinum"
      ],
      [ // Tier 3 - Deep (600-1000m)
        "Darkstone", "Emberglass", "Froststeel",
        "Starcrystal", "Sunshard", "Voidgem"
      ],
      // Additional tiers for deeper levels (same minerals but higher chance of veins)
      [ // Tier 4 (1000-1500m)
        "Silver", "Topaz", "Gold", "Sapphire",
        "Emerald", "Ruby", "Diamond", "Platinum",
        "Darkstone", "Emberglass", "Froststeel",
        "Starcrystal", "Sunshard", "Voidgem"
      ],
      [ // Tier 5 (1500-2100m)
        "Gold", "Sapphire", "Emerald", "Ruby",
        "Diamond", "Platinum", "Darkstone", "Emberglass",
        "Froststeel", "Starcrystal", "Sunshard", "Voidgem"
      ],
      [ // Tier 6 (2100-2800m)
        "Diamond", "Platinum", "Darkstone", "Emberglass",
        "Froststeel", "Starcrystal", "Sunshard", "Voidgem"
      ],
      [ // Tier 7 (2800-3600m)
        "Darkstone", "Emberglass", "Froststeel",
        "Starcrystal", "Sunshard", "Voidgem"
      ],
      [ // Tier 8 (3600-4500m)
        "Emberglass", "Froststeel", "Starcrystal",
        "Sunshard", "Voidgem"
      ],
      [ // Tier 9 (4500-5500m)
        "Starcrystal", "Sunshard", "Voidgem"
      ],
      [ // Tier 10 (5500m+)
        "Voidgem"
      ]
    ];

    // Audio elements
    const bgMusic = document.getElementById("bg-music");
    const miningSound = document.getElementById("mining-sound");
    const sellSound = document.getElementById("sell-sound");
    const upgradeSound = document.getElementById("upgrade-sound");

    // Load music file from user input
    function loadMusicFile(input) {
      const file = input.files[0];
      if (!file) return;

      console.log('Loading music file:', file.name);

      if (!file.type.startsWith('audio/')) {
        alert('Please select an audio file (MP3)');
        return;
      }

      // Create object URL for the file
      const url = URL.createObjectURL(file);
      console.log('Created object URL:', url);

      // Set the audio source
      bgMusic.src = url;
      bgMusic.load();

      console.log('✅ Music file loaded successfully:', file.name);
      alert('Music file loaded! You can now enable music.');

      // Clean up previous URL if exists
      if (bgMusic.previousObjectURL) {
        URL.revokeObjectURL(bgMusic.previousObjectURL);
      }
      bgMusic.previousObjectURL = url;
    }

    // Try to load the default music file
    function tryLoadDefaultMusic() {
      console.log('Attempting to load default music file...');

      // Try different possible paths
      const possiblePaths = [
        'light-contours-ai-213696.mp3',
        './light-contours-ai-213696.mp3',
        '../light-contours-ai-213696.mp3'
      ];

      let pathIndex = 0;

      function tryNextPath() {
        if (pathIndex >= possiblePaths.length) {
          console.log('❌ Default music file not found. Please use the file input to load your MP3.');
          return;
        }

        const path = possiblePaths[pathIndex];
        console.log('Trying path:', path);

        bgMusic.src = path;

        // Set up one-time event listeners
        const onLoad = () => {
          console.log('✅ Default music loaded from:', path);
          bgMusic.removeEventListener('canplaythrough', onLoad);
          bgMusic.removeEventListener('error', onError);
        };

        const onError = () => {
          console.log('❌ Failed to load from:', path);
          bgMusic.removeEventListener('canplaythrough', onLoad);
          bgMusic.removeEventListener('error', onError);
          pathIndex++;
          tryNextPath();
        };

        bgMusic.addEventListener('canplaythrough', onLoad, { once: true });
        bgMusic.addEventListener('error', onError, { once: true });

        bgMusic.load();
      }

      tryNextPath();
    }

    // Set up background music with proper looping
    function initializeMusic() {
      console.log('Initializing music system...');
      console.log('bgMusic element:', bgMusic);

      if (!bgMusic) {
        console.error('❌ bgMusic element not found!');
        return;
      }

      // Set volume to a comfortable level
      bgMusic.volume = 0.4;
      console.log('Set volume to:', bgMusic.volume);

      // Ensure looping is enabled
      bgMusic.loop = true;
      console.log('Loop enabled:', bgMusic.loop);

      // Try to load the default MP3 file if it exists
      tryLoadDefaultMusic();

      // Handle music loading errors gracefully
      bgMusic.addEventListener('error', function(e) {
        console.error('❌ Background music failed to load:', e);
        console.error('Error details:', e.target.error);
        console.error('Network state:', bgMusic.networkState);
        console.error('Ready state:', bgMusic.readyState);

        // Try to provide helpful error message
        if (e.target.error) {
          switch(e.target.error.code) {
            case e.target.error.MEDIA_ERR_ABORTED:
              console.error('Audio loading was aborted');
              break;
            case e.target.error.MEDIA_ERR_NETWORK:
              console.error('Network error while loading audio');
              break;
            case e.target.error.MEDIA_ERR_DECODE:
              console.error('Audio file is corrupted or unsupported format');
              break;
            case e.target.error.MEDIA_ERR_SRC_NOT_SUPPORTED:
              console.error('Audio file format not supported');
              break;
          }
        }
      });

      // Handle successful loading
      bgMusic.addEventListener('canplaythrough', function() {
        console.log('✅ Background music loaded successfully and ready to loop');
        console.log('Music duration:', bgMusic.duration, 'seconds');
      });

      // Backup looping mechanism in case HTML loop attribute fails
      bgMusic.addEventListener('ended', function() {
        console.log('Music ended event fired');
        if (musicEnabled) {
          console.log('Restarting music for continuous loop');
          bgMusic.currentTime = 0;
          bgMusic.play().catch(e => console.warn('Failed to restart music:', e));
        }
      });

      // Handle loading state
      bgMusic.addEventListener('loadstart', function() {
        console.log('Started loading background music...');
      });

      // Test if the file can be loaded
      console.log('Music source:', bgMusic.src || bgMusic.currentSrc);
      console.log('Music ready state:', bgMusic.readyState);
    }

    function startMusic() {
      console.log('startMusic called. musicEnabled:', musicEnabled, 'bgMusic:', bgMusic);

      if (!musicEnabled || !bgMusic) {
        console.log('Music not enabled or bgMusic not found');
        return;
      }

      // Check if the audio file is loaded
      if (bgMusic.readyState < 2) {
        console.log('Audio not ready, waiting for load...');
        bgMusic.addEventListener('canplay', function() {
          console.log('Audio now ready, attempting to play...');
          attemptPlay();
        }, { once: true });

        // Force load the audio
        bgMusic.load();
        return;
      }

      attemptPlay();

      function attemptPlay() {
        try {
          // Reset to beginning for fresh start
          bgMusic.currentTime = 0;
          console.log('Reset music to beginning');

          // Play the music with looping
          const playPromise = bgMusic.play();
          console.log('Called bgMusic.play(), promise:', playPromise);

          // Handle play promise (required by modern browsers)
          if (playPromise !== undefined) {
            playPromise.then(() => {
              console.log('✅ Background music started successfully and will loop continuously');
            }).catch(error => {
              console.error('❌ Could not start background music:', error);
              console.error('Error name:', error.name);
              console.error('Error message:', error.message);

              // Try alternative approach
              if (error.name === 'NotAllowedError') {
                console.log('🔄 Trying user interaction approach...');
                alert('Click OK to enable music (browser requires user interaction)');
                bgMusic.play().catch(e => console.error('Still failed:', e));
              }
            });
          }
        } catch (error) {
          console.error('❌ Error starting music:', error);
          console.error('Trying to reload audio file...');
          bgMusic.load();
        }
      }
    }

    function stopMusic() {
      console.log('stopMusic called. bgMusic:', bgMusic, 'paused:', bgMusic ? bgMusic.paused : 'N/A');

      if (bgMusic && !bgMusic.paused) {
        bgMusic.pause();
        console.log('✅ Background music stopped');
      } else {
        console.log('Music already paused or bgMusic not found');
      }
    }

    // Initialize inventory
    function initializeInventory() {
      inventory = {};
      for (const mineral in mineralData) {
        inventory[mineral] = 0;
      }
    }

    // Initialize mining display
    function initializeMiningDisplay() {
      const chunksContainer = document.getElementById('mining-chunks');
      chunksContainer.innerHTML = '';
      miningChunks = [];
      chunksMinedCount = 0;
      currentMiningIndex = 0;

      // Get current tier color
      const tierColor = tierColors[Math.min(currentTier, tierColors.length - 1)];

      // Create 150 chunks (15x10 grid)
      for (let i = 0; i < totalChunks; i++) {
        const chunk = document.createElement('div');
        chunk.className = 'chunk';
        chunk.dataset.index = i;

        // Set tier-based color
        chunk.style.background = tierColor;

        // Randomly place some mineral chunks (10% chance)
        if (Math.random() < 0.1) {
          chunk.classList.add('mineral');
        }

        chunksContainer.appendChild(chunk);
        miningChunks.push({
          element: chunk,
          mined: false,
          hasMineral: chunk.classList.contains('mineral')
        });
      }

      updateMiningDisplay();
    }

    function updateMiningDisplay() {
      // Update progress text
      const progressPercent = Math.floor((chunksMinedCount / totalChunks) * 100);
      document.getElementById('depth-progress-text').textContent = `${progressPercent}%`;
      document.getElementById('chunks-mined').textContent = `${chunksMinedCount}/${totalChunks}`;

      // If all chunks are mined, reset the display for next tier
      if (chunksMinedCount >= totalChunks) {
        setTimeout(() => {
          initializeMiningDisplay();
        }, 1000);
      }
    }

    function mineChunk() {
      // Find the next chunk to mine in left-to-right order
      if (currentMiningIndex >= totalChunks) return;

      const chunkToMine = miningChunks[currentMiningIndex];
      if (!chunkToMine || chunkToMine.mined) {
        currentMiningIndex++;
        return;
      }

      // Mark as mined
      chunkToMine.mined = true;
      chunkToMine.element.classList.add('mined');
      chunksMinedCount++;
      currentMiningIndex++;

      // Create mining particle effect
      createMiningParticle(chunkToMine.element);

      // Check if it had a mineral
      if (chunkToMine.hasMineral) {
        // Add sparkle effect before mining
        setTimeout(() => {
          chunkToMine.element.style.background = 'rgba(255, 215, 0, 0.3)';
        }, 200);
      }

      updateMiningDisplay();
    }

    function createMiningParticle(chunkElement) {
      const rect = chunkElement.getBoundingClientRect();
      const particle = document.createElement('div');
      particle.className = 'mining-particle';

      // Position particle at chunk location
      particle.style.left = rect.left + 'px';
      particle.style.top = rect.top + 'px';
      particle.style.setProperty('--tx', (Math.random() - 0.5) * 2);
      particle.style.setProperty('--ty', (Math.random() - 0.5) * 2);

      document.body.appendChild(particle);

      // Remove particle after animation
      setTimeout(() => {
        if (particle.parentNode) {
          particle.parentNode.removeChild(particle);
        }
      }, 1000);
    }

    // Initialize game
    initializeInventory();
    initializeMusic();
    initializeMiningDisplay();
    initializeEventSystem();
    updateMaxInventorySlots(); // Ensure inventory slots are properly set
    updateUI();
    startGameTimer();

    // Auto-save every 30 seconds
    setInterval(saveGame, 30000);

    // Core Game Functions
    function updateUI() {
      document.getElementById("coin-count").textContent = Math.floor(coins);

      // Update new upgrade displays
      document.getElementById("drill-level").textContent = drillLevel;
      document.getElementById("drill-cost").textContent = Math.floor(BASE_DRILL_COST * Math.pow(1.3, drillLevel));
      document.getElementById("vein-boost-cost").textContent = Math.floor(BASE_VEIN_BOOST_COST * Math.pow(1.1, veinChance / 0.08));
      document.getElementById("inventory-level").textContent = inventoryUpgradeLevel;
      document.getElementById("inventory-cost").textContent = Math.floor(BASE_INVENTORY_UPGRADE_COST * Math.pow(1.4, inventoryUpgradeLevel));
      document.getElementById("autosell-cost").textContent = AUTO_SELL_COST;
      document.getElementById("prestige-btn").disabled = coins < PRESTIGE_REQUIREMENT;
      document.getElementById("autosell-btn").disabled = autoSellEnabled;

      // Show/hide Auto-Sell based on tier (only show when tier > 1)
      const autoSellItem = document.getElementById("autosell-shop-item");
      if (currentTier > 1) {
        autoSellItem.style.display = "block";
      } else {
        autoSellItem.style.display = "none";
      }

      // Update depth display
      document.getElementById("current-depth").textContent = `${Math.floor(currentDepth)}m`;

      if (currentTier < DEPTH_TIERS.length) {
        document.getElementById("next-tier").textContent = `${DEPTH_TIERS[currentTier]}m`;
      } else {
        document.getElementById("next-tier").textContent = `Max Depth`;
      }

      // Update inventory count display
      const usedSlots = getUsedInventorySlots();
      document.getElementById("inventory-count").textContent = `(${usedSlots}/${maxInventorySlots})`;

      // Update inventory list with sell buttons
      const inventoryList = document.getElementById("inventory");
      inventoryList.innerHTML = "";

      // Group minerals by tier and sort by value
      const sortedMinerals = [];
      for (let tier = 0; tier <= currentTier; tier++) {
        if (mineralTiers[tier]) {
          mineralTiers[tier].forEach(mineralName => {
            if (inventory[mineralName] > 0) {
              sortedMinerals.push({
                name: mineralName,
                value: mineralData[mineralName].value,
                color: mineralData[mineralName].color,
                tier: tier,
                count: inventory[mineralName]
              });
            }
          });
        }
      }

      // Sort by value (highest first)
      sortedMinerals.sort((a, b) => b.value - a.value);

      // Display inventory items
      sortedMinerals.forEach(mineral => {
        const li = document.createElement("li");
        const totalValue = mineral.count * mineral.value;

        const itemContainer = document.createElement("div");
        itemContainer.className = "inventory-item";

        const icon = document.createElement("span");
        icon.className = "mineral-icon";
        icon.style.backgroundColor = mineral.color;
        icon.title = `${mineral.name} (Tier ${mineral.tier})`;

        const nameSpan = document.createElement("span");
        nameSpan.textContent = mineral.name;
        nameSpan.className = `mineral-${mineral.name.replace(/\s+/g, '')}`;

        const valueSpan = document.createElement("span");
        valueSpan.className = "inventory-value";
        valueSpan.textContent = `$${totalValue}`;

        itemContainer.appendChild(icon);
        itemContainer.appendChild(nameSpan);
        li.appendChild(itemContainer);
        li.appendChild(valueSpan);

        const sellBtn = document.createElement("button");
        sellBtn.textContent = "Sell";
        sellBtn.style.padding = "2px 8px";
        sellBtn.style.fontSize = "12px";
        sellBtn.onclick = (e) => {
          e.stopPropagation();
          sellMineral(mineral.name, mineral.value);
        };

        li.appendChild(sellBtn);
        inventoryList.appendChild(li);
      });
    }

    // Inventory Management Functions
    function getUsedInventorySlots() {
      let usedSlots = 0;
      for (const mineralName in inventory) {
        if (inventory[mineralName] > 0) {
          usedSlots++;
        }
      }
      return usedSlots;
    }

    function hasInventorySpace() {
      return getUsedInventorySlots() < maxInventorySlots;
    }

    function canAddMineralToInventory(mineralName) {
      // If mineral already exists in inventory, we can always add more
      if (inventory[mineralName] > 0) {
        return true;
      }
      // If mineral doesn't exist, check if we have space for a new type
      return hasInventorySpace();
    }

    function updateMaxInventorySlots() {
      maxInventorySlots = BASE_INVENTORY_SLOTS + (inventoryUpgradeLevel * INVENTORY_UPGRADE_SLOTS);
    }

    function buyInventoryUpgrade() {
      const cost = Math.floor(BASE_INVENTORY_UPGRADE_COST * Math.pow(1.4, inventoryUpgradeLevel));
      if (coins >= cost) {
        coins -= cost;
        inventoryUpgradeLevel++;
        updateMaxInventorySlots();
        updateUI();

        // Play upgrade sound if enabled
        if (soundEffectsEnabled) {
          upgradeSound.currentTime = 0;
          upgradeSound.play();
        }

        showNotification(`Inventory expanded to ${maxInventorySlots} slots! (+5 slots)`);
      }
    }

    function mine() {
      // Check for active events that halt mining
      const haltingEvent = activeEvents.find(event => event.effect === 'mining_halt');
      if (haltingEvent) {
        return; // Mining is halted
      }

      // Calculate total vein chance
      const totalVeinChance = veinChance;
      const isVein = Math.random() < totalVeinChance;
      const amount = isVein ? 5 : 1;

      // Mine a chunk in the visual display
      mineChunk();

      // Increase depth with each mine
      currentDepth += DEPTH_PER_MINE;
      gameStats.deepestDepth = Math.max(gameStats.deepestDepth, currentDepth);

      // Check if we've reached a new depth tier
      if (currentTier < DEPTH_TIERS.length && currentDepth >= DEPTH_TIERS[currentTier]) {
        currentTier++;

        // Reset timing and vein chance to base values on tier progression
        miningSpeed = 1;
        veinChance = 0;

        // Clear events panel content when reaching new tier
        clearEventsOnNewTier();

        showNotification(`Reached new depth tier ${currentTier}! New minerals available. Mining speed and vein chance reset to base values.`, "success");

        // Restart mining interval with new tier timing
        if (autoMining) {
          startMiningInterval();
        }
      }

      // Determine available minerals based on current tier
      const availableMinerals = [];
      for (let tier = 0; tier <= currentTier; tier++) {
        if (mineralTiers[tier]) {
          availableMinerals.push(...mineralTiers[tier]);
        }
      }

      if (availableMinerals.length === 0) {
        console.error("No minerals available at this depth");
        return;
      }

      // Play mining sound if enabled
      if (soundEffectsEnabled) {
        miningSound.currentTime = 0;
        miningSound.play();
      }

      const mineralName = availableMinerals[Math.floor(Math.random() * availableMinerals.length)];
      const mineral = mineralData[mineralName];

      // Check if we can add this mineral to inventory
      if (!canAddMineralToInventory(mineralName)) {
        // Throttle inventory full notifications to once every 5 seconds
        const now = Date.now();
        if (now - lastInventoryFullWarning > 5000) {
          showNotification("Inventory full! Sell minerals to make space.", "warning");
          lastInventoryFullWarning = now;
        }
        return; // Stop mining if inventory is full
      }

      // Track vein statistics
      if (isVein) {
        gameStats.totalVeinsFound++;
        gameStats.totalVeinMinerals += amount;
        gameStats.largestVeinFound = Math.max(gameStats.largestVeinFound, amount);
        gameStats.averageVeinSize = gameStats.totalVeinMinerals / gameStats.totalVeinsFound;
      }

      gameStats.totalMineralsMined += amount;
      gameStats.veinSuccessRate = gameStats.totalVeinsFound / (gameStats.totalMineralsMined / 5); // Approximate

      // Track highest value mineral
      if (!gameStats.highestValueMineral || mineral.value >
         (mineralData[gameStats.highestValueMineral]?.value || 0)) {
        gameStats.highestValueMineral = mineralName;
      }

      inventory[mineralName] = (inventory[mineralName] || 0) + amount;
      mineralsDiscovered.add(mineralName);

      // Track found minerals
      foundMinerals[mineralName] = (foundMinerals[mineralName] || 0) + amount;

      // Check for artifact discovery (2% base chance + tier bonus + vein multiplier)
      if (window.enhancedMining) {
        const artifactResult = window.enhancedMining(currentTier, isVein);
        if (artifactResult) {
          console.log('Artifact discovered during mining:', artifactResult.item.name);
        }
      }

      // Warn when inventory is getting full (throttled)
      const usedSlots = getUsedInventorySlots();
      if (usedSlots >= maxInventorySlots - 1 && usedSlots < maxInventorySlots) {
        const now = Date.now();
        if (now - lastInventoryWarning > 10000) { // Once every 10 seconds
          showNotification("Inventory almost full! Consider selling minerals.", "warning");
          lastInventoryWarning = now;
        }
      }

      // Achievement checks
      if (!achievements.firstMineral) {
        achievements.firstMineral = true;
        showNotification("Achievement Unlocked: First Mineral Mined!", "success");
      }
      if (isVein && !achievements.firstVein) {
        achievements.firstVein = true;
        showNotification("Achievement Unlocked: First Vein Found!", "success");
      }

      // Add particles when mining
      const btn = document.getElementById("auto-btn");
      const rect = btn.getBoundingClientRect();
      createMiningParticles(
        rect.left + rect.width/2,
        rect.top + rect.height/2
      );

      if (isVein) {
        showNotification(`Found a vein! +5 ${mineralName} (Value: ${mineral.value} each)`);
      } else {
        showNotification(`Mined 1 ${mineralName} (Value: ${mineral.value})`);
      }

      updateUI();

      if (autoSellEnabled) {
        setTimeout(() => {
          let totalValue = 0;
          for (const m in mineralData) {
            if (inventory[m] > 0) {
              totalValue += inventory[m] * mineralData[m].value * 0.5 * prestigeBonus;
              inventory[m] = 0;
            }
          }
          if (totalValue > 0) {
            coins += totalValue;
            totalEarnedCoins += totalValue;
            showCoinGain(totalValue);
            showNotification(`Auto-sold minerals for ${Math.floor(totalValue)} coins`);
            updateUI();

            if (!achievements.firstSale) {
              achievements.firstSale = true;
              showNotification("Achievement Unlocked: First Auto-Sale!", "success");
            }
          }
        }, 1000);
      }
    }

    function createMiningParticles(x, y, count = 8) {
      for (let i = 0; i < count; i++) {
        const particle = document.createElement("div");
        particle.className = "mining-particle";
        particle.style.left = `${x}px`;
        particle.style.top = `${y}px`;
        particle.style.setProperty('--tx', Math.random() * 2 - 1);
        particle.style.setProperty('--ty', Math.random() * -2);
        particle.style.backgroundColor = `hsl(${Math.random() * 60 + 30}, 100%, 50%)`;
        particle.style.width = `${Math.random() * 6 + 4}px`;
        particle.style.height = particle.style.width;
        document.body.appendChild(particle);

        setTimeout(() => {
          particle.remove();
        }, 1000);
      }
    }

    function toggleAutoMining() {
      autoMining = !autoMining;
      const btn = document.getElementById("auto-btn");
      if (autoMining) {
        btn.textContent = "Stop Mining";
        btn.setAttribute("aria-pressed", "true");
        startMiningInterval();
      } else {
        btn.textContent = "Start Mining";
        btn.setAttribute("aria-pressed", "false");
        clearInterval(autoInterval);
        clearInterval(timerUpdateInterval);
        updateTimerDisplay("--");
      }
    }

    // Timer functions
    function startTimerUpdate() {
      timerUpdateInterval = setInterval(updateTimer, 100); // Update every 100ms for smooth countdown
    }

    function updateTimer() {
      if (!autoMining) return;

      const timeLeft = Math.max(0, nextMineTime - Date.now());
      const seconds = (timeLeft / 1000).toFixed(1);

      // Check for active events that affect timer display
      const haltingEvent = activeEvents.find(event => event.effect === 'mining_halt');
      const slowingEvent = activeEvents.find(event => event.effect === 'slow_mining');

      const timerElement = document.getElementById("mining-timer");

      if (haltingEvent) {
        updateTimerDisplay("HALTED");
        timerElement.className = "mining-timer halted";
      } else if (slowingEvent) {
        updateTimerDisplay(`${seconds}s`);
        timerElement.className = "mining-timer slowed";
      } else {
        updateTimerDisplay(`${seconds}s`);
        timerElement.className = "mining-timer";
      }
    }

    function updateTimerDisplay(text) {
      const countdown = document.getElementById("timer-countdown");
      if (countdown) {
        countdown.textContent = text;
      }
    }

    function startMiningInterval() {
      clearInterval(autoInterval);
      clearInterval(timerUpdateInterval);

      // Calculate base time for current tier (30s + 15s per tier)
      const tierBaseMiningTime = BASE_MINING_TIME + (currentTier * TIER_TIME_INCREASE);

      // Calculate total speed bonus from all sources
      const drillBonus = 1 + (drillLevel * DRILL_SPEED_BOOST);
      const totalSpeedMultiplier = miningSpeed * prestigeBonus * drillBonus;

      // Apply event effects
      const slowingEvent = activeEvents.find(event => event.effect === 'slow_mining');
      const eventMultiplier = slowingEvent ? slowingEvent.multiplier : 1;

      // Calculate final interval time with proper rounding to prevent timer drift
      const intervalTime = Math.max(100, Math.round((tierBaseMiningTime / totalSpeedMultiplier) / eventMultiplier));

      // Set next mine time
      nextMineTime = Date.now() + intervalTime;

      // Start timer update
      startTimerUpdate();

      autoInterval = setInterval(() => {
        mine();
        // Recalculate interval time in case of changes during mining
        const newTierBaseMiningTime = BASE_MINING_TIME + (currentTier * TIER_TIME_INCREASE);
        const newDrillBonus = 1 + (drillLevel * DRILL_SPEED_BOOST);
        const newTotalSpeedMultiplier = miningSpeed * prestigeBonus * newDrillBonus;
        const newSlowingEvent = activeEvents.find(event => event.effect === 'slow_mining');
        const newEventMultiplier = newSlowingEvent ? newSlowingEvent.multiplier : 1;
        const newIntervalTime = Math.max(100, Math.round((newTierBaseMiningTime / newTotalSpeedMultiplier) / newEventMultiplier));

        nextMineTime = Date.now() + newIntervalTime;
      }, intervalTime);
    }

    function getCurrentMiningTime() {
      const tierBaseMiningTime = BASE_MINING_TIME + (currentTier * TIER_TIME_INCREASE);
      const drillBonus = 1 + (drillLevel * DRILL_SPEED_BOOST);
      const totalSpeedMultiplier = miningSpeed * prestigeBonus * drillBonus;

      // Apply event effects
      const slowingEvent = activeEvents.find(event => event.effect === 'slow_mining');
      const eventMultiplier = slowingEvent ? slowingEvent.multiplier : 1;

      return (tierBaseMiningTime / totalSpeedMultiplier) / eventMultiplier;
    }

    function sellMinerals() {
      let totalValue = 0;
      for (const mineralName in mineralData) {
        const count = inventory[mineralName];
        if (count > 0) {
          totalValue += count * mineralData[mineralName].value * prestigeBonus;
          inventory[mineralName] = 0;
        }
      }

      if (totalValue > 0) {
        coins += totalValue;
        totalEarnedCoins += totalValue;
        showCoinGain(totalValue);
        updateUI();

        // Play sell sound if enabled
        if (soundEffectsEnabled) {
          sellSound.currentTime = 0;
          sellSound.play();
        }

        if (!achievements.firstSale) {
          achievements.firstSale = true;
          showNotification("Achievement Unlocked: First Sale!", "success");
        }
      }
    }

    function sellMineral(mineralName, value) {
      const amount = inventory[mineralName];
      if (amount > 0) {
        const total = amount * value * (autoSellEnabled ? 0.5 : 1) * prestigeBonus;
        coins += total;
        totalEarnedCoins += total;
        inventory[mineralName] = 0;
        showCoinGain(total);
        showNotification(`Sold ${amount} ${mineralName} for ${Math.floor(total)} coins`);

        // Play sell sound if enabled
        if (soundEffectsEnabled) {
          sellSound.currentTime = 0;
          sellSound.play();
        }

        if (!achievements.firstSale) {
          achievements.firstSale = true;
          showNotification("Achievement Unlocked: First Mineral Sold!", "success");
        }

        updateUI();
      }
    }

    // Shop Functions
    function buyDrillEnhancement() {
      const cost = Math.floor(BASE_DRILL_COST * Math.pow(1.3, drillLevel));
      if (coins >= cost) {
        coins -= cost;
        drillLevel++;
        updateUI();

        // Restart mining interval with new timing to prevent timer reset bug
        if (autoMining) {
          clearInterval(autoInterval);
          clearInterval(timerUpdateInterval);
          startMiningInterval();
        }

        // Play upgrade sound if enabled
        if (soundEffectsEnabled) {
          upgradeSound.currentTime = 0;
          upgradeSound.play();
        }

        showNotification(`Drill Enhancement upgraded to level ${drillLevel}! +15% mining speed.`);

        if (!achievements.firstUpgrade) {
          achievements.firstUpgrade = true;
          showNotification("Achievement Unlocked: First Upgrade!", "success");
        }
      }
    }



    function buyVeinChanceBoost() {
      if (veinChanceBoostCooldown) return;
      const cost = Math.floor(BASE_VEIN_BOOST_COST * Math.pow(1.1, veinChance / 0.08));
      if (coins >= cost) {
        coins -= cost;
        veinChance += 0.08;
        veinChance = Math.min(veinChance, 0.8);
        updateUI();
        veinChanceBoostCooldown = true;
        const btn = document.getElementById("veinChanceBoostBtn");
        btn.disabled = true;

        // Play upgrade sound if enabled
        if (soundEffectsEnabled) {
          upgradeSound.currentTime = 0;
          upgradeSound.play();
        }

        showNotification("Vein chance increased!");

        setTimeout(() => {
          veinChanceBoostCooldown = false;
          btn.disabled = false;
        }, 30000);
      }
    }

    function buyAutoSell() {
      if (coins >= AUTO_SELL_COST && !autoSellEnabled) {
        coins -= AUTO_SELL_COST;
        autoSellEnabled = true;
        document.getElementById("autosell-btn").disabled = true;

        // Play upgrade sound if enabled
        if (soundEffectsEnabled) {
          upgradeSound.currentTime = 0;
          upgradeSound.play();
        }

        showNotification("Auto-Sell enabled! Minerals will be automatically sold at 50% value.");
        updateUI();
      }
    }



    function prestigeReset() {
      if (coins < PRESTIGE_REQUIREMENT) return;

      const bonusPercent = Math.round((prestigeBonus - 1) * 100);
      const newBonusPercent = bonusPercent + 10;

      if (!confirm(`Prestige will reset your game but give you a permanent +10% bonus (total: ${newBonusPercent}%). Proceed?`)) return;

      prestigeBonus *= 1.1;
      coins = 0;
      miningSpeed = 1;
      currentDepth = 0;
      currentTier = 0;
      veinChance = 0;
      autoSellEnabled = false;

      for (const key in inventory) {
        inventory[key] = 0;
      }

      if (autoMining) {
        clearInterval(autoInterval);
        autoMining = false;
        document.getElementById("auto-btn").textContent = "Start Mining";
      }

      if (!achievements.firstPrestige) {
        achievements.firstPrestige = true;
        showNotification("Achievement Unlocked: First Prestige!", "success");
      }

      showNotification(`Prestige complete! You now have a ${newBonusPercent}% bonus`);
      updateUI();
      saveGame();
    }

    // Save/Load System
    function saveGame() {
      const gameData = {
        coins,
        miningSpeed,
        currentDepth,
        currentTier,
        inventory,
        veinChance,
        prestigeBonus,
        totalEarnedCoins,
        autoSellEnabled,
        mineralsDiscovered: Array.from(mineralsDiscovered),
        lastSaveTime: Date.now(),
        achievements,
        gameStats,
        soundEffectsEnabled,
        musicEnabled,
        currentScreenScale,
        isFullscreen,
        drillLevel,
        inventoryUpgradeLevel,
        maxInventorySlots,
        foundMinerals,
        eventHistory,
        lastEventTime,
        // Artifact and Storage data
        artifactData: window.saveGameData ? window.saveGameData() : null
      };
      localStorage.setItem('idleMinerSave', JSON.stringify(gameData));
      lastSaveTime = Date.now();
      showNotification("Game saved!");
    }

    function loadGame() {
      const saved = localStorage.getItem('idleMinerSave');
      if (saved) {
        try {
          const gameData = JSON.parse(saved);
          coins = gameData.coins || 0;
          miningSpeed = gameData.miningSpeed || 1;
          currentDepth = gameData.currentDepth || 0;
          currentTier = gameData.currentTier || 0;
          inventory = gameData.inventory || {};
          veinChance = gameData.veinChance || 0;
          prestigeBonus = gameData.prestigeBonus || 1;
          totalEarnedCoins = gameData.totalEarnedCoins || 0;
          autoSellEnabled = gameData.autoSellEnabled || false;
          mineralsDiscovered = new Set(gameData.mineralsDiscovered || []);
          lastSaveTime = gameData.lastSaveTime || Date.now();
          achievements = gameData.achievements || {
            firstMineral: false,
            firstVein: false,
            firstUpgrade: false,
            firstSale: false,
            firstPrestige: false
          };
          gameStats = gameData.gameStats || {
            totalMineralsMined: 0,
            totalVeinsFound: 0,
            timePlayed: 0,
            highestValueMineral: "",
            deepestDepth: 0
          };
          soundEffectsEnabled = gameData.soundEffectsEnabled !== undefined ? gameData.soundEffectsEnabled : true;
          musicEnabled = gameData.musicEnabled !== undefined ? gameData.musicEnabled : false;
          currentScreenScale = gameData.currentScreenScale || 100;
          isFullscreen = gameData.isFullscreen || false;
          drillLevel = gameData.drillLevel || 0;
          inventoryUpgradeLevel = gameData.inventoryUpgradeLevel || 0;
          maxInventorySlots = gameData.maxInventorySlots || BASE_INVENTORY_SLOTS;
          foundMinerals = gameData.foundMinerals || {};
          eventHistory = gameData.eventHistory || [];
          lastEventTime = gameData.lastEventTime || 0;

          // Update max inventory slots based on upgrade level
          updateMaxInventorySlots();

          // Ensure gameStats has all new properties
          if (gameStats.veinSuccessRate === undefined) gameStats.veinSuccessRate = 0;
          if (gameStats.largestVeinFound === undefined) gameStats.largestVeinFound = 0;
          if (gameStats.totalVeinMinerals === undefined) gameStats.totalVeinMinerals = 0;
          if (gameStats.averageVeinSize === undefined) gameStats.averageVeinSize = 0;

          // Initialize any missing minerals
          for (const mineral in mineralData) {
            if (!inventory.hasOwnProperty(mineral)) {
              inventory[mineral] = 0;
            }
          }

          // Calculate offline progress
          const offlineTime = Date.now() - lastSaveTime;
          if (offlineTime > 30000) {
            const offlineMiningCycles = Math.floor(offlineTime / (MINING_INTERVAL / (miningSpeed * prestigeBonus)));
            if (offlineMiningCycles > 0) {
              const mineralsGained = {};
              for (let i = 0; i < offlineMiningCycles; i++) {
                const isVein = Math.random() < veinChance;
                const amount = isVein ? 5 : 1;
                currentDepth += DEPTH_PER_MINE;

                // Check if we've reached a new depth tier
                if (currentTier < DEPTH_TIERS.length && currentDepth >= DEPTH_TIERS[currentTier]) {
                  currentTier++;
                }

                // Determine available minerals based on current tier
                const availableMinerals = [];
                for (let tier = 0; tier <= currentTier; tier++) {
                  if (mineralTiers[tier]) {
                    availableMinerals.push(...mineralTiers[tier]);
                  }
                }

                if (availableMinerals.length > 0) {
                  const mineral = availableMinerals[Math.floor(Math.random() * availableMinerals.length)];
                  mineralsGained[mineral] = (mineralsGained[mineral] || 0) + amount;
                  mineralsDiscovered.add(mineral);
                }
              }

              let message = `While you were away (${formatTime(offlineTime)}):\n`;
              message += `Dug ${offlineMiningCycles}m deeper\n`;
              for (const [mineral, count] of Object.entries(mineralsGained)) {
                inventory[mineral] = (inventory[mineral] || 0) + count;
                message += `+${count} ${mineral}\n`;
              }
              showNotification(message);
            }
          }

          // Restore music state
          if (musicEnabled) {
            startMusic();
            document.getElementById("music-btn").textContent = "🔊 Music On";
          } else {
            stopMusic();
            document.getElementById("music-btn").textContent = "🔈 Music Off";
          }

          // Update SFX button
          document.getElementById("sfx-btn").textContent = soundEffectsEnabled ? "🔉 SFX On" : "🔇 SFX Off";

          // Load artifact and storage data
          if (gameData.artifactData && window.loadGameData) {
            window.loadGameData(gameData.artifactData);
          }

          updateUI();
          showNotification("Game loaded successfully!");
        } catch (e) {
          console.error("Failed to load game:", e);
          showNotification("Error loading save", "error");
        }
      } else {
        showNotification("No save file found", "error");
      }
    }

    function formatTime(ms) {
      const seconds = Math.floor(ms / 1000) % 60;
      const minutes = Math.floor(ms / (1000 * 60)) % 60;
      const hours = Math.floor(ms / (1000 * 60 * 60));

      return `${hours > 0 ? hours + 'h ' : ''}${minutes > 0 ? minutes + 'm ' : ''}${seconds}s`;
    }

    function startGameTimer() {
      setInterval(() => {
        gameStats.timePlayed += 1000;
      }, 1000);
    }

    // Visual Effects
    function showCoinGain(amount) {
      const gainElement = document.createElement("div");
      gainElement.className = "coin-gain";
      gainElement.textContent = `+${Math.floor(amount)}`;
      gainElement.style.left = `${Math.random() * 100}%`;
      gainElement.style.top = "60%";
      document.body.appendChild(gainElement);

      setTimeout(() => {
        gainElement.remove();
      }, 1000);
    }

    function showNotification(message, type = "info") {
      const notification = document.createElement("div");
      notification.className = `notification ${type}`;
      notification.textContent = message;
      document.body.appendChild(notification);

      setTimeout(() => {
        notification.remove();
      }, 3000);
    }



    function updateStatsDisplay() {
      const content = document.getElementById("stats-content");

      // Calculate detailed mining time breakdown
      const tierBaseMiningTime = BASE_MINING_TIME + (currentTier * TIER_TIME_INCREASE);
      const drillBonus = 1 + (drillLevel * DRILL_SPEED_BOOST);
      const totalSpeedMultiplier = miningSpeed * prestigeBonus * drillBonus;
      const currentMiningTime = tierBaseMiningTime / totalSpeedMultiplier;

      content.innerHTML = `
        <div style="margin-bottom: 20px; padding: 15px; background: rgba(255, 255, 255, 0.05); border-radius: 8px; border: 1px solid rgba(255, 255, 255, 0.1);">
          <h3 style="margin-top: 0; color: #fff;">⏱️ Mining Time Analysis</h3>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
            <div><strong>Base Time (Tier ${currentTier}):</strong></div>
            <div>${(tierBaseMiningTime / 1000).toFixed(1)}s</div>

            <div><strong>Base Speed Multiplier:</strong></div>
            <div>${miningSpeed.toFixed(2)}x</div>

            <div><strong>Drill Enhancement Bonus:</strong></div>
            <div>${drillBonus.toFixed(2)}x (Level ${drillLevel})</div>

            <div><strong>Prestige Bonus:</strong></div>
            <div>${prestigeBonus.toFixed(2)}x</div>

            <div><strong>Total Speed Multiplier:</strong></div>
            <div>${totalSpeedMultiplier.toFixed(2)}x</div>

            <div style="border-top: 1px solid rgba(255,255,255,0.2); padding-top: 8px;"><strong>Current Mining Time:</strong></div>
            <div style="border-top: 1px solid rgba(255,255,255,0.2); padding-top: 8px; color: #4CAF50;"><strong>${(currentMiningTime / 1000).toFixed(1)}s</strong></div>
          </div>
          <div style="margin-top: 10px; font-size: 12px; color: #999;">
            Formula: Base Time ÷ (Base Speed × Drill Enhancement × Prestige)
          </div>
        </div>

        <div style="margin-bottom: 20px; padding: 15px; background: rgba(255, 215, 0, 0.1); border-radius: 8px; border: 1px solid rgba(255, 215, 0, 0.3);">
          <h3 style="margin-top: 0; color: #FFD700;">💎 Vein Statistics</h3>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
            <div><strong>Total Veins Found:</strong></div>
            <div>${gameStats.totalVeinsFound}</div>

            <div><strong>Vein Success Rate:</strong></div>
            <div>${(gameStats.veinSuccessRate * 100).toFixed(1)}%</div>

            <div><strong>Largest Vein Found:</strong></div>
            <div>${gameStats.largestVeinFound} minerals</div>

            <div><strong>Total Vein Minerals:</strong></div>
            <div>${gameStats.totalVeinMinerals}</div>

            <div><strong>Average Vein Size:</strong></div>
            <div>${gameStats.averageVeinSize.toFixed(1)} minerals</div>

            <div><strong>Current Vein Chance:</strong></div>
            <div>${(veinChance * 100).toFixed(1)}%</div>
          </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 14px;">
          <div>
            <p>⏱️ <strong>Time Played:</strong> ${formatTime(gameStats.timePlayed)}</p>
            <p>⛏️ <strong>Total Minerals Mined:</strong> ${gameStats.totalMineralsMined}</p>
            <p>💎 <strong>Veins Found:</strong> ${gameStats.totalVeinsFound}</p>
            <p>💰 <strong>Highest Value Mineral:</strong> ${gameStats.highestValueMineral || "None yet"}</p>
          </div>
          <div>
            <p>⬇️ <strong>Deepest Depth:</strong> ${gameStats.deepestDepth}m</p>
            <p>✨ <strong>Minerals Discovered:</strong> ${mineralsDiscovered.size}/${mineralTiers.flat().length}</p>
            <p>🏆 <strong>Total Coins Earned:</strong> ${Math.floor(totalEarnedCoins)}</p>
            <p>🚀 <strong>Prestige Bonus:</strong> ${Math.round((prestigeBonus - 1) * 100)}%</p>
          </div>
        </div>
      `;
    }

    function updateEncyclopedia() {
      const grid = document.getElementById("mineral-grid");
      grid.innerHTML = "";

      mineralTiers.forEach((tier, tierIndex) => {
        tier.forEach(mineralName => {
          const mineral = mineralData[mineralName];
          const card = document.createElement("div");
          card.className = `mineral-card ${mineralsDiscovered.has(mineralName) ? 'discovered' : 'undiscovered'}`;

          card.innerHTML = `
            <div class="mineral-header">
              <span class="mineral-icon" style="background-color: ${mineral.color}"></span>
              <h3 class="mineral-${mineralName.replace(/\s+/g, '')}">${mineralName}</h3>
            </div>
            <p>Tier ${tierIndex}</p>
            ${mineralsDiscovered.has(mineralName) ?
              `<p>Value: ${mineral.value}</p>
               <p>${getMineralDescription(mineralName)}</p>` :
              `<p>???</p>`}
          `;

          grid.appendChild(card);
        });
      });
    }

    function getMineralDescription(name) {
      const descriptions = {
        "Feldspar": "Common rock-forming mineral",
        "Gypsum": "Soft sulfate mineral",
        "Quartz": "Hard crystalline mineral",
        "Mica": "Shiny layered mineral",
        "Calcite": "Carbonate mineral with many crystal forms",
        "Fluorite": "Colorful mineral that fluoresces under UV light",
        "Magnetite": "Magnetic iron ore",
        "Hematite": "Iron oxide with metallic luster",
        "Dolomite": "Carbonate mineral similar to calcite",
        "Apatite": "Phosphate mineral important for life",
        "Barite": "Dense sulfate mineral",
        "Talc": "Softest known mineral",
        "Bauxite": "Primary ore of aluminum",
        "Sphalerite": "Primary ore of zinc",
        "Chalcopyrite": "Brassy yellow copper ore",
        "Galena": "Lead ore with cubic crystals",
        "Silver": "Precious metal with high conductivity",
        "Topaz": "Hard silicate mineral in many colors",
        "Gold": "Precious metal prized for millennia",
        "Sapphire": "Precious blue gemstone",
        "Emerald": "Green variety of beryl",
        "Ruby": "Red variety of corundum",
        "Diamond": "The hardest known natural material",
        "Platinum": "Rare, valuable metal",
        "Darkstone": "Mysterious mineral that absorbs light",
        "Emberglass": "Volcanic glass that glows from within",
        "Froststeel": "Metal that stays perpetually cold",
        "Starcrystal": "Extraterrestrial mineral with cosmic energy",
        "Sunshard": "Radiates warmth and light",
        "Voidgem": "Mysterious mineral from the cosmos"
      };
      return descriptions[name] || "A valuable mineral";
    }

    // Reset and Music
    function resetGame() {
      if (!confirm("Are you sure you want to reset the game? This cannot be undone!")) return;
      coins = 0;
      miningSpeed = 1;
      currentDepth = 0;
      currentTier = 0;
      veinChance = 0;
      totalEarnedCoins = 0;
      autoSellEnabled = false;
      mineralsDiscovered = new Set();
      drillLevel = 0;
      inventoryUpgradeLevel = 0;
      maxInventorySlots = BASE_INVENTORY_SLOTS;
      foundMinerals = {};
      activeEvents = [];
      eventHistory = [];
      lastEventTime = 0;
      achievements = {
        firstMineral: false,
        firstVein: false,
        firstUpgrade: false,
        firstSale: false,
        firstPrestige: false
      };
      gameStats = {
        totalMineralsMined: 0,
        totalVeinsFound: 0,
        timePlayed: 0,
        highestValueMineral: "",
        deepestDepth: 0,
        veinSuccessRate: 0,
        largestVeinFound: 0,
        totalVeinMinerals: 0,
        averageVeinSize: 0
      };

      for (const key in inventory) {
        inventory[key] = 0;
      }

      updateUI();
      if (autoMining) {
        clearInterval(autoInterval);
        autoMining = false;
        document.getElementById("auto-btn").textContent = "Start Mining";
      }

      showNotification("Game reset!");
    }

    function toggleMusic() {
      const musicBtn = document.getElementById("music-btn");

      console.log('Toggle music clicked. Current state:', musicEnabled);
      console.log('bgMusic element:', bgMusic);

      if (musicEnabled) {
        musicEnabled = false;
        stopMusic();
        musicBtn.textContent = "🔈 Music Off";
        console.log('Music disabled');
      } else {
        musicEnabled = true;
        startMusic();
        musicBtn.textContent = "🔊 Music On";
        console.log('Music enabled');
      }

      // Save game state if saveGame function exists
      if (typeof saveGame === 'function') {
        saveGame();
      }
    }

    function toggleSoundEffects() {
      const sfxBtn = document.getElementById("sfx-btn");
      soundEffectsEnabled = !soundEffectsEnabled;
      sfxBtn.textContent = soundEffectsEnabled ? "🔉 SFX On" : "🔇 SFX Off";
      saveGame();
    }

    // Screen size management with slider
    function updateScreenSize(scale) {
      const container = document.querySelector('.ui-container');
      const baseWidth = 1200;
      const baseHeight = 800;

      currentScreenScale = parseInt(scale);

      const newWidth = Math.round(baseWidth * (scale / 100));
      const newHeight = Math.round(baseHeight * (scale / 100));

      // Update container size
      container.style.width = newWidth + 'px';
      container.style.height = newHeight + 'px';

      // Update display text
      document.getElementById('size-display').textContent = `${newWidth}x${newHeight}`;

      // Save preference
      if (typeof saveGame === 'function') {
        saveGame();
      }

      console.log(`✅ Screen size updated to: ${newWidth}x${newHeight} (${scale}%)`);
    }

    function toggleFullscreen() {
      const container = document.querySelector('.ui-container');
      const body = document.body;
      const btn = document.getElementById('fullscreen-btn');

      if (isFullscreen) {
        // Exit fullscreen
        container.style.position = 'relative';
        container.style.top = '';
        container.style.left = '';
        container.style.transform = '';
        container.style.zIndex = '';
        body.style.overflow = '';
        body.style.padding = '20px';
        body.style.margin = '';

        // Restore previous size
        updateScreenSize(currentScreenScale);

        btn.textContent = '🖥️ Toggle Fullscreen';
        isFullscreen = false;

        document.removeEventListener('keydown', handleEscapeKey);
      } else {
        // Enter fullscreen
        container.style.position = 'fixed';
        container.style.top = '50%';
        container.style.left = '50%';
        container.style.transform = 'translate(-50%, -50%)';
        container.style.width = '95vw';
        container.style.height = '95vh';
        container.style.zIndex = '1000';
        body.style.overflow = 'hidden';
        body.style.padding = '0';
        body.style.margin = '0';

        btn.textContent = '🖥️ Exit Fullscreen';
        isFullscreen = true;

        document.addEventListener('keydown', handleEscapeKey);
      }

      if (typeof saveGame === 'function') {
        saveGame();
      }
    }

    function handleEscapeKey(event) {
      if (event.key === 'Escape' && isFullscreen) {
        toggleFullscreen();
      }
    }

    // Panel management for popups
    function closeAllPanels() {
      const panels = ['shop-panel', 'settings-panel', 'stats-panel', 'encyclopedia-panel', 'found-minerals-panel', 'events-panel', 'storage-panel'];
      const overlay = document.getElementById('panel-overlay');

      panels.forEach(panelId => {
        const panel = document.getElementById(panelId);
        if (panel) {
          panel.style.display = 'none';
          panel.setAttribute('aria-hidden', 'true');
        }
      });

      overlay.style.display = 'none';

      // Reset button states
      const buttons = ['shop-btn', 'settings-btn', 'stats-btn', 'encyclopedia-btn', 'found-minerals-btn', 'events-btn', 'storage-btn'];
      buttons.forEach(btnId => {
        const btn = document.getElementById(btnId);
        if (btn) {
          btn.setAttribute('aria-pressed', 'false');
        }
      });
    }

    function openPanel(panelId, buttonId) {
      closeAllPanels(); // Close any open panels first

      const panel = document.getElementById(panelId);
      const overlay = document.getElementById('panel-overlay');
      const button = document.getElementById(buttonId);

      if (panel && overlay) {
        panel.style.display = 'block';
        panel.setAttribute('aria-hidden', 'false');
        overlay.style.display = 'block';

        if (button) {
          button.setAttribute('aria-pressed', 'true');
        }

        // Special handling for different panels
        if (panelId === 'stats-panel') {
          updateStatsDisplay();
        } else if (panelId === 'encyclopedia-panel') {
          updateEncyclopedia();
        } else if (panelId === 'found-minerals-panel') {
          updateFoundMineralsDisplay();
        } else if (panelId === 'events-panel') {
          updateEventsDisplay();
        } else if (panelId === 'storage-panel' && window.updateStorageDisplay) {
          window.updateStorageDisplay();
        }
      }
    }

    // Panel toggle functions
    function toggleShop() {
      openPanel('shop-panel', 'shop-btn');
    }

    function toggleSettings() {
      openPanel('settings-panel', 'settings-btn');
    }

    function toggleStats() {
      openPanel('stats-panel', 'stats-btn');
    }

    function toggleEncyclopedia() {
      openPanel('encyclopedia-panel', 'encyclopedia-btn');
    }

    function toggleFoundMinerals() {
      openPanel('found-minerals-panel', 'found-minerals-btn');
    }

    function toggleEvents() {
      openPanel('events-panel', 'events-btn');
    }

    function toggleStorage() {
      openPanel('storage-panel', 'storage-btn');
    }

    // Found Minerals Display
    function updateFoundMineralsDisplay() {
      const list = document.getElementById('found-minerals-list');
      list.innerHTML = '';

      if (Object.keys(foundMinerals).length === 0) {
        list.innerHTML = '<p style="color: #999; font-style: italic;">No minerals found yet. Start mining to discover minerals!</p>';
        return;
      }

      // Sort minerals by total count found (highest first)
      const sortedMinerals = Object.entries(foundMinerals)
        .sort(([,a], [,b]) => b - a)
        .filter(([,count]) => count > 0);

      sortedMinerals.forEach(([mineralName, count]) => {
        const mineral = mineralData[mineralName];
        const item = document.createElement('div');
        item.style.cssText = `
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          margin: 4px 0;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 6px;
          border-left: 4px solid ${mineral.color};
        `;

        item.innerHTML = `
          <span style="display: flex; align-items: center; gap: 8px;">
            <span style="width: 12px; height: 12px; background: ${mineral.color}; border-radius: 50%; display: inline-block;"></span>
            <strong>${mineralName}</strong>
          </span>
          <span style="color: #4CAF50; font-weight: bold;">${count}</span>
        `;

        list.appendChild(item);
      });
    }

    // Events Display and System
    function updateEventsDisplay() {
      const activeList = document.getElementById('active-events-list');
      const recentList = document.getElementById('recent-events-list');

      // Update active events
      activeList.innerHTML = '';
      if (activeEvents.length === 0) {
        activeList.innerHTML = '<p style="color: #999; font-style: italic;">No active events</p>';
      } else {
        activeEvents.forEach(event => {
          const timeLeft = Math.max(0, event.endTime - Date.now());
          const item = document.createElement('div');
          item.style.cssText = `
            padding: 10px;
            margin: 5px 0;
            background: rgba(255, 100, 100, 0.1);
            border: 1px solid rgba(255, 100, 100, 0.3);
            border-radius: 6px;
          `;

          item.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span style="font-size: 18px;">${event.icon}</span>
              <div style="flex: 1; margin-left: 10px;">
                <strong>${event.name}</strong>
                <p style="margin: 2px 0; font-size: 12px; color: #ccc;">${event.description}</p>
              </div>
              <span style="color: #ff6b6b; font-weight: bold;">${Math.ceil(timeLeft / 1000)}s</span>
            </div>
          `;

          activeList.appendChild(item);
        });
      }

      // Update recent events
      recentList.innerHTML = '';
      if (eventHistory.length === 0) {
        recentList.innerHTML = '<p style="color: #999; font-style: italic;">No recent events</p>';
      } else {
        eventHistory.slice(-5).reverse().forEach(event => {
          const item = document.createElement('div');
          item.style.cssText = `
            padding: 8px;
            margin: 3px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            font-size: 12px;
          `;

          const timeAgo = formatTime(Date.now() - event.timestamp);
          item.innerHTML = `
            <span>${event.icon} ${event.name}</span>
            <span style="float: right; color: #999;">${timeAgo} ago</span>
          `;

          recentList.appendChild(item);
        });
      }
    }

    // Event System Functions
    function clearEventsOnNewTier() {
      // Clear active events
      activeEvents.forEach(event => {
        // Close any active event popups
        if (currentEventPopup && currentEventPopup.eventId === event.id) {
          closeEventPopup();
        }
      });
      activeEvents = [];

      // Clear event history
      eventHistory = [];

      // Update events display if panel is open
      const eventsPanel = document.getElementById('events-panel');
      if (eventsPanel && eventsPanel.style.display === 'block') {
        updateEventsDisplay();
      }

      console.log('Events cleared for new tier');
    }

    function triggerRandomEvent() {
      if (activeEvents.length > 0) return; // Don't trigger if event is active
      if (Date.now() - lastEventTime < eventCooldown) return; // Cooldown check

      // 10% chance to trigger an event each check
      if (Math.random() > 0.1) return;

      const eventType = EVENT_TYPES[Math.floor(Math.random() * EVENT_TYPES.length)];
      const event = {
        ...eventType,
        startTime: Date.now(),
        endTime: Date.now() + eventType.duration,
        timestamp: Date.now()
      };

      activeEvents.push(event);
      eventHistory.push(event);
      lastEventTime = Date.now();

      // Create event popup window
      createEventPopup(event);

      // If mining is active, restart interval to apply event effects
      if (autoMining) {
        startMiningInterval();
      }

      // Set timer to remove event when it expires
      setTimeout(() => {
        removeEvent(event);
      }, eventType.duration);
    }

    function removeEvent(eventToRemove) {
      const index = activeEvents.findIndex(event => event.startTime === eventToRemove.startTime);
      if (index !== -1) {
        activeEvents.splice(index, 1);

        // Close event popup if it's open
        if (currentEventPopup && currentEventPopup.eventId === eventToRemove.id) {
          closeEventPopup();
        }

        showNotification(`${eventToRemove.icon} ${eventToRemove.name} has ended.`, "success");

        // Restart mining interval to remove event effects
        if (autoMining) {
          startMiningInterval();
        }
      }
    }

    // Event popup functions
    function createEventPopup(event) {
      // Close any existing event popup
      if (currentEventPopup) {
        closeEventPopup();
      }

      // Create popup overlay
      const overlay = document.createElement('div');
      overlay.className = 'event-popup-overlay';
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
      `;

      // Create popup window
      const popup = document.createElement('div');
      popup.className = 'event-popup';
      popup.style.cssText = `
        background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
        border: 3px solid #ff6b6b;
        border-radius: 12px;
        padding: 25px;
        max-width: 400px;
        width: 90%;
        text-align: center;
        color: white;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8);
        animation: eventPopupSlide 0.3s ease-out;
      `;

      // Create popup content
      const skipCost = 200;
      const canAffordSkip = coins >= skipCost;

      popup.innerHTML = `
        <div style="font-size: 48px; margin-bottom: 15px;">${event.icon}</div>
        <h2 style="margin: 0 0 15px 0; color: #ff6b6b; font-size: 24px;">${event.name}</h2>
        <p style="margin: 0 0 20px 0; line-height: 1.5; font-size: 16px;">${event.description}</p>
        <div style="background: rgba(255, 107, 107, 0.1); border: 1px solid rgba(255, 107, 107, 0.3); border-radius: 8px; padding: 15px; margin-bottom: 20px;">
          <div style="font-size: 14px; color: #ccc; margin-bottom: 5px;">Time Remaining:</div>
          <div id="event-timer" style="font-size: 20px; font-weight: bold; color: #ff6b6b; font-family: 'Courier New', monospace;">--</div>
        </div>
        <div style="display: flex; gap: 10px; justify-content: center;">
          <button onclick="closeEventPopup()" style="
            background: linear-gradient(to bottom, #666, #444);
            border: 1px solid #888;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">Acknowledge</button>
          <button onclick="skipEventForCoins('${event.id}', ${event.startTime})"
                  ${canAffordSkip ? '' : 'disabled'}
                  style="
            background: ${canAffordSkip ? 'linear-gradient(to bottom, #4CAF50, #45a049)' : '#666'};
            border: 1px solid ${canAffordSkip ? '#4CAF50' : '#888'};
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: ${canAffordSkip ? 'pointer' : 'not-allowed'};
            font-size: 14px;
            opacity: ${canAffordSkip ? '1' : '0.6'};
          ">💰 Skip (${skipCost} coins)</button>
        </div>
      `;

      // Add CSS animation
      const style = document.createElement('style');
      style.textContent = `
        @keyframes eventPopupSlide {
          from {
            transform: translateY(-50px);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
      `;
      document.head.appendChild(style);

      overlay.appendChild(popup);
      document.body.appendChild(overlay);

      // Store reference to current popup
      currentEventPopup = {
        overlay: overlay,
        popup: popup,
        event: event,
        eventId: event.id,
        timerInterval: null
      };

      // Start countdown timer
      startEventTimer(event);
    }

    function startEventTimer(event) {
      if (!currentEventPopup) return;

      currentEventPopup.timerInterval = setInterval(() => {
        const timeLeft = Math.max(0, event.endTime - Date.now());
        const seconds = Math.ceil(timeLeft / 1000);

        const timerElement = document.getElementById('event-timer');
        if (timerElement) {
          if (seconds > 0) {
            timerElement.textContent = `${seconds}s`;
          } else {
            timerElement.textContent = "Event Ending...";
            // Auto-close popup when event ends
            setTimeout(() => {
              if (currentEventPopup && currentEventPopup.eventId === event.id) {
                closeEventPopup();
              }
            }, 1000);
          }
        }
      }, 100);
    }

    function closeEventPopup() {
      if (currentEventPopup) {
        // Clear timer
        if (currentEventPopup.timerInterval) {
          clearInterval(currentEventPopup.timerInterval);
        }

        // Remove popup from DOM
        if (currentEventPopup.overlay && currentEventPopup.overlay.parentNode) {
          currentEventPopup.overlay.parentNode.removeChild(currentEventPopup.overlay);
        }

        currentEventPopup = null;
      }
    }

    function skipEventForCoins(eventId, startTime) {
      const skipCost = 200;

      // Check if player has enough coins
      if (coins < skipCost) {
        showNotification('Not enough coins to skip event!', 'warning');
        return false;
      }

      // Find the specific event
      const event = activeEvents.find(e => e.id === eventId && e.startTime === startTime);
      if (!event) {
        showNotification('Event not found!', 'error');
        return false;
      }

      // Spend the coins
      coins -= skipCost;

      // Remove the event
      const index = activeEvents.findIndex(e => e.id === eventId && e.startTime === startTime);
      if (index !== -1) {
        activeEvents.splice(index, 1);
      }

      // Close the popup
      closeEventPopup();

      // Update UI
      updateUI();

      // Show success notification
      showNotification(`💰 Paid ${skipCost} coins to skip ${event.icon} ${event.name}!`, 'success');

      // Restart mining interval to remove event effects
      if (autoMining) {
        startMiningInterval();
      }

      console.log('Event skipped for coins:', event);
      return true;
    }

    // Add Vein definition to Encyclopedia
    function addVeinDefinitionToEncyclopedia() {
      const encyclopediaPanel = document.getElementById('encyclopedia-panel');
      const existingVeinInfo = encyclopediaPanel.querySelector('.vein-definition');

      if (!existingVeinInfo) {
        const veinDefinition = document.createElement('div');
        veinDefinition.className = 'vein-definition';
        veinDefinition.style.cssText = `
          margin-bottom: 20px;
          padding: 15px;
          background: rgba(255, 215, 0, 0.1);
          border: 1px solid rgba(255, 215, 0, 0.3);
          border-radius: 8px;
        `;

        veinDefinition.innerHTML = `
          <h3 style="margin-top: 0; color: #FFD700;">💎 Mineral Veins</h3>
          <p style="margin: 10px 0; line-height: 1.5;">
            <strong>Veins</strong> are rich mineral deposits that yield <strong>5x more minerals</strong> than regular mining operations.
            When you discover a vein, you'll extract 5 units of the mineral instead of just 1.
          </p>
          <ul style="margin: 10px 0; padding-left: 20px; line-height: 1.5;">
            <li><strong>Base Vein Chance:</strong> Starts at 0%, increased by upgrades</li>
            <li><strong>Vein Chance Boost:</strong> +8% chance per purchase (max 80%)</li>
            <li><strong>Visual Indicator:</strong> Golden chunks in the mining display contain veins</li>
            <li><strong>Statistics:</strong> Track your vein discovery rate in the Stats panel</li>
          </ul>
          <p style="margin: 10px 0; line-height: 1.5; color: #4CAF50;">
            <strong>Tip:</strong> Investing in vein chance upgrades significantly increases your mineral yield and coin income!
          </p>
        `;

        // Insert after the visual mining simulation info
        const mineralGrid = document.getElementById('mineral-grid');
        encyclopediaPanel.insertBefore(veinDefinition, mineralGrid);
      }
    }

    // Initialize event system
    function initializeEventSystem() {
      // Check for random events every 30 seconds
      setInterval(triggerRandomEvent, 30000);

      // Add vein definition to encyclopedia
      addVeinDefinitionToEncyclopedia();
    }

    // Storage system functions for artifact selling
    function sellAllArtifactsFromStorage() {
      // This function is called from the storage panel
      if (window.sellAllArtifactsFromStorage) {
        return window.sellAllArtifactsFromStorage();
      } else {
        showNotification('Storage system not fully loaded', 'warning');
        return 0;
      }
    }

    // Initialize screen size
    updateScreenSize(currentScreenScale);
    document.getElementById('size-slider').value = currentScreenScale;

    // Initialize music button state
    document.getElementById("music-btn").textContent = musicEnabled ? "🔊 Music On" : "🔈 Music Off";
    document.getElementById("sfx-btn").textContent = soundEffectsEnabled ? "🔉 SFX On" : "🔇 SFX Off";
  </script>

  <!-- Artifact and Storage System Modules -->
  <script type="module" src="js/artifacts.js"></script>
  <script type="module" src="js/storage.js"></script>
  <script type="module" src="js/inventory.js"></script>
  <script type="module" src="js/main.js"></script>
</body>
</html>