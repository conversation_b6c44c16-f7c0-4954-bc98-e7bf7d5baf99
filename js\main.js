// Main game controller for Idle Miner
// Integrates all systems including artifacts, storage, and inventory

import { 
  shouldSpawnArtifact, 
  selectRandomArtifact, 
  getArtifactSpawnChance,
  getAllArtifacts 
} from './artifacts.js';

import { 
  initializeStorage, 
  toggleStorage, 
  saveStorageData, 
  loadStorageData,
  updateStorageDisplay 
} from './storage.js';

import { 
  initializeInventory, 
  addToInventory, 
  hasInventorySpace, 
  getInventoryData, 
  loadInventoryData,
  updateInventoryDisplay 
} from './inventory.js';

// Game initialization
document.addEventListener('DOMContentLoaded', function() {
  console.log('Idle Miner - Artifact System Loaded');
  
  // Initialize all systems
  initializeStorage();
  initializeInventory();
  
  // Set up keyboard shortcuts
  setupKeyboardShortcuts();
  
  // Load saved game data
  loadGameData();
  
  // Set up periodic saves
  setInterval(saveGameData, 30000); // Save every 30 seconds
});

// Enhanced mining function with artifact support
function enhancedMining(currentTier, isVeinDiscovery = false) {
  // Check for artifact spawn
  if (shouldSpawnArtifact(currentTier, isVeinDiscovery)) {
    const artifact = selectRandomArtifact();
    
    // Add artifact to inventory
    addToInventory(artifact.id, 1, true);
    
    // Show notification
    const spawnChance = (getArtifactSpawnChance(currentTier, isVeinDiscovery) * 100).toFixed(1);
    showNotification(`🎉 Rare Discovery! Found ${artifact.name}! (${spawnChance}% chance)`);
    
    console.log(`Artifact found: ${artifact.name} (Tier ${currentTier}, Vein: ${isVeinDiscovery})`);
    
    return { type: 'artifact', item: artifact };
  }
  
  return null; // No artifact found
}

// Set up keyboard shortcuts
function setupKeyboardShortcuts() {
  document.addEventListener('keydown', function(event) {
    // Only trigger if not typing in an input field
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
      return;
    }
    
    switch(event.key) {
      case '5':
        toggleStorage();
        break;
    }
  });
}

// Save game data including artifacts and storage
function saveGameData() {
  try {
    const gameData = {
      // Existing game data would go here
      storage: saveStorageData(),
      inventory: getInventoryData(),
      timestamp: Date.now()
    };
    
    localStorage.setItem('idleMinerSave', JSON.stringify(gameData));
    console.log('Game saved with artifact data');
  } catch (error) {
    console.error('Failed to save game:', error);
  }
}

// Load game data including artifacts and storage
function loadGameData() {
  try {
    const savedData = localStorage.getItem('idleMinerSave');
    if (savedData) {
      const gameData = JSON.parse(savedData);
      
      // Load storage data
      if (gameData.storage) {
        loadStorageData(gameData.storage);
      }
      
      // Load inventory data
      if (gameData.inventory) {
        loadInventoryData(gameData.inventory);
      }
      
      console.log('Game loaded with artifact data');
    }
  } catch (error) {
    console.error('Failed to load game:', error);
  }
}

// Show notification function
function showNotification(message, duration = 3000) {
  // Create notification element
  const notification = document.createElement('div');
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    z-index: 10000;
    font-size: 14px;
    max-width: 300px;
    word-wrap: break-word;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  `;
  
  document.body.appendChild(notification);
  
  // Remove notification after duration
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, duration);
}

// Close all panels function
function closeAllPanels() {
  const panels = document.querySelectorAll('.panel');
  const buttons = document.querySelectorAll('.bottom-buttons button');
  
  panels.forEach(panel => {
    panel.setAttribute('aria-hidden', 'true');
    panel.style.display = 'none';
  });
  
  buttons.forEach(button => {
    button.setAttribute('aria-pressed', 'false');
  });
}

// Integration with existing mining system
function integrateWithExistingMining() {
  // This function would integrate with the existing mining logic
  // It should be called whenever a mining operation completes
  
  // Example integration:
  // const originalMineFunction = window.mine;
  // window.mine = function() {
  //   const result = originalMineFunction.apply(this, arguments);
  //   
  //   // Check for artifacts after normal mining
  //   const artifactResult = enhancedMining(currentTier, false);
  //   
  //   return result;
  // };
}

// Make functions available globally
if (typeof window !== 'undefined') {
  window.enhancedMining = enhancedMining;
  window.showNotification = showNotification;
  window.closeAllPanels = closeAllPanels;
  window.saveGameData = saveGameData;
  window.loadGameData = loadGameData;
  
  // Storage functions
  window.toggleStorage = toggleStorage;
  
  // Debug functions
  window.debugArtifacts = function() {
    console.log('Available artifacts:', getAllArtifacts());
    console.log('Storage data:', saveStorageData());
    console.log('Inventory data:', getInventoryData());
  };
}

// Export functions for module use
export {
  enhancedMining,
  showNotification,
  closeAllPanels,
  saveGameData,
  loadGameData,
  setupKeyboardShortcuts
};
