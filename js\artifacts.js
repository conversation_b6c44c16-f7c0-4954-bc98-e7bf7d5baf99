// Artifact system for Idle Miner
// Handles artifact definitions, spawning, and management

// Artifact definitions with name, description, rarity, and sell price
export const ARTIFACTS = [
  {
    id: 'ancient_pickaxe',
    name: 'Ancient Pickaxe',
    description: 'A weathered mining tool from a bygone era. Its handle bears the marks of countless excavations.',
    rarity: 'Common',
    sellPrice: 500,
    icon: '⛏️'
  },
  {
    id: 'crystal_compass',
    name: 'Crystal Compass',
    description: 'A mystical navigation device that points toward mineral veins. The crystal glows faintly with inner light.',
    rarity: 'Uncommon',
    sellPrice: 1200,
    icon: '🧭'
  },
  {
    id: 'dwarven_lantern',
    name: 'Dwarven Lantern',
    description: 'An ornate lantern with eternal flame. Crafted by master dwarven smiths in the deep mines.',
    rarity: 'Rare',
    sellPrice: 2500,
    icon: '🏮'
  },
  {
    id: 'void_stone_tablet',
    name: 'Void Stone Tablet',
    description: 'A mysterious tablet inscribed with ancient mining techniques. The text seems to shift when not observed directly.',
    rarity: 'Epic',
    sellPrice: 5000,
    icon: '📜'
  },
  {
    id: 'starfall_meteorite',
    name: 'Starfall Meteorite',
    description: 'A fragment of a fallen star, pulsing with cosmic energy. It radiates warmth and seems to whisper secrets of the universe.',
    rarity: 'Legendary',
    sellPrice: 10000,
    icon: '☄️'
  }
];

// Legacy compatibility - convert new format to old format for existing code
export const ARTIFACT_DATA = {};
ARTIFACTS.forEach(artifact => {
  ARTIFACT_DATA[artifact.name] = {
    value: artifact.sellPrice,
    color: getArtifactRarityColor(artifact.rarity),
    description: artifact.description,
    rarity: artifact.rarity,
    tier: getRarityTier(artifact.rarity)
  };
});

function getRarityTier(rarity) {
  switch (rarity) {
    case 'Common': return 0;
    case 'Uncommon': return 1;
    case 'Rare': return 2;
    case 'Epic': return 3;
    case 'Legendary': return 4;
    default: return 0;
  }
}

function getArtifactRarityColor(rarity) {
  switch (rarity) {
    case "Common": return "#FFFFFF";
    case "Uncommon": return "#1EFF00";
    case "Rare": return "#0070DD";
    case "Epic": return "#A335EE";
    case "Legendary": return "#FF8000";
    default: return "#FFFFFF";
  }
}

// Artifact tiers based on depth (similar to minerals)
export const ARTIFACT_TIERS = [
  ["Ancient Pickaxe"], // Tier 0 - Surface (0-100m)
  ["Ancient Pickaxe", "Crystal Compass"], // Tier 1 - Shallow (100-300m)
  ["Crystal Compass", "Dwarven Lantern"], // Tier 2 - Medium (300-600m)
  ["Dwarven Lantern", "Void Stone Tablet"], // Tier 3 - Deep (600-1000m)
  ["Void Stone Tablet", "Starfall Meteorite"], // Tier 4+ - Very Deep (1000m+)
];

// Artifact spawn rates (2% base chance as requested)
export const ARTIFACT_SPAWN_RATES = {
  base: 0.02, // 2% base chance
  veinMultiplier: 2, // 2x chance during vein discovery
  tierBonus: 0.001 // +0.1% per tier
};

// Rarity weights for artifact selection
export const RARITY_WEIGHTS = {
  'Common': 50,
  'Uncommon': 30,
  'Rare': 15,
  'Epic': 4,
  'Legendary': 1
};

// Get available artifacts for current tier
export function getAvailableArtifacts(tier) {
  const maxTier = Math.min(tier, ARTIFACT_TIERS.length - 1);
  return ARTIFACT_TIERS[maxTier] || [];
}

// Calculate artifact spawn chance
export function calculateArtifactChance(tier, isVein = false) {
  let chance = ARTIFACT_SPAWN_RATES.base;

  // Add tier bonus
  chance += tier * ARTIFACT_SPAWN_RATES.tierBonus;

  // Multiply for vein discovery
  if (isVein) {
    chance *= ARTIFACT_SPAWN_RATES.veinMultiplier;
  }

  // Cap at 5% maximum
  return Math.min(chance, 0.05);
}

// Get random artifact from current tier
export function getRandomArtifact(tier) {
  const availableArtifacts = getAvailableArtifacts(tier);
  if (availableArtifacts.length === 0) return null;

  return availableArtifacts[Math.floor(Math.random() * availableArtifacts.length)];
}

// Get artifact rarity color
export function getArtifactRarityColor(artifactName) {
  const artifact = ARTIFACT_DATA[artifactName];
  if (!artifact) return "#FFFFFF";

  switch (artifact.rarity) {
    case "Common": return "#FFFFFF";
    case "Uncommon": return "#1EFF00";
    case "Rare": return "#0070DD";
    case "Epic": return "#A335EE";
    case "Legendary": return "#FF8000";
    default: return "#FFFFFF";
  }
}

// Get all artifacts for encyclopedia
export function getAllArtifacts() {
  return Object.keys(ARTIFACT_DATA).map(name => ({
    name,
    ...ARTIFACT_DATA[name]
  }));
}

// Validate artifact name
export function isValidArtifact(artifactName) {
  return artifactName in ARTIFACT_DATA;
}

// Get artifact stats
export function getArtifactStats(artifacts) {
  const stats = {
    totalArtifacts: 0,
    totalValue: 0,
    uniqueTypes: 0,
    mostValuable: null,
    rarityCount: {
      Common: 0,
      Uncommon: 0,
      Rare: 0,
      Epic: 0,
      Legendary: 0
    }
  };

  let highestValue = 0;

  for (const artifactName in artifacts) {
    if (artifacts[artifactName] > 0) {
      const count = artifacts[artifactName];
      const artifact = ARTIFACT_DATA[artifactName];

      if (artifact) {
        const totalValue = count * artifact.value;

        stats.totalArtifacts += count;
        stats.totalValue += totalValue;
        stats.uniqueTypes++;
        stats.rarityCount[artifact.rarity] += count;

        if (artifact.value > highestValue) {
          highestValue = artifact.value;
          stats.mostValuable = artifactName;
        }
      }
    }
  }

  return stats;
}

// New artifact system functions

// Get artifact spawn chance based on current conditions
export function getArtifactSpawnChance(currentTier, isVeinDiscovery = false) {
  let chance = ARTIFACT_SPAWN_RATES.base;

  // Add tier bonus
  chance += currentTier * ARTIFACT_SPAWN_RATES.tierBonus;

  // Apply vein multiplier if applicable
  if (isVeinDiscovery) {
    chance *= ARTIFACT_SPAWN_RATES.veinMultiplier;
  }

  return Math.min(chance, 0.1); // Cap at 10%
}

// Select a random artifact based on rarity weights
export function selectRandomArtifact() {
  // Create weighted array
  const weightedArtifacts = [];

  ARTIFACTS.forEach(artifact => {
    const weight = RARITY_WEIGHTS[artifact.rarity] || 1;
    for (let i = 0; i < weight; i++) {
      weightedArtifacts.push(artifact);
    }
  });

  // Select random artifact
  const randomIndex = Math.floor(Math.random() * weightedArtifacts.length);
  return weightedArtifacts[randomIndex];
}

// Check if an artifact should spawn during mining
export function shouldSpawnArtifact(currentTier, isVeinDiscovery = false) {
  const spawnChance = getArtifactSpawnChance(currentTier, isVeinDiscovery);
  return Math.random() < spawnChance;
}

// Get artifact by ID
export function getArtifactById(id) {
  return ARTIFACTS.find(artifact => artifact.id === id);
}

// Format artifact for display
export function formatArtifactDisplay(artifact, quantity = 1) {
  const quantityText = quantity > 1 ? ` x${quantity}` : '';
  return `${artifact.icon} ${artifact.name}${quantityText} (${artifact.rarity}) - ${artifact.sellPrice} coins`;
}

// Calculate total value of artifacts
export function calculateArtifactValue(artifactCounts) {
  let totalValue = 0;

  Object.entries(artifactCounts).forEach(([artifactId, count]) => {
    const artifact = getArtifactById(artifactId);
    if (artifact && count > 0) {
      totalValue += artifact.sellPrice * count;
    }
  });

  return totalValue;
}
